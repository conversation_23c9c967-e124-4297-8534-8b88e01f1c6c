,Questions,Query,collection,query_type,Output_query
0,Which business processes have no or single process step?,"[\n  {\n    ""$match"": {\n      ""processSteps"": {\n        ""$size"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""stepCount"": {
                ""$sum"": {
                    ""$size"": ""$processSteps""
                }
            },
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            }
        }
    },
    {
        ""$match"": {
            ""stepCount"": {
                ""$lte"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": ""$_id"",
            ""businessProcessDefinition"": 1,
            ""stepCount"": 1
        }
    }
]
"
1,provide me any 5 business processes which have no or single process step?,"[\n  {\n    ""$match"": {\n      ""processSteps"": {\n        ""$size"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  },\n  {\n    ""$limit"": 5\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""stepCount"": {
                ""$sum"": {
                    ""$size"": ""$processSteps""
                }
            },
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            }
        }
    },
    {
        ""$match"": {
            ""stepCount"": {
                ""$lte"": 1
            }
        }
    },
    {
        ""$limit"": 5
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": ""$_id"",
            ""businessProcessDefinition"": 1,
            ""stepCount"": 1
        }
    }
]
"
2,provide me 5 business processes which have no or single process step by sorting them by business process definition in ascending?,"[\n  {\n    ""$match"": {\n      ""processSteps"": {\n        ""$size"": 1\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""businessProcessDefinition"": 1\n    }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    { 
        ""$group"": { 
            ""_id"": ""$simplropsId"", 
            ""stepCount"": { ""$sum"": { ""$size"": ""$processSteps"" } }, 
            ""businessProcessDefinition"": { ""$first"": ""$businessProcessDefinition"" } 
        } 
    },
    { 
        ""$match"": { 
            ""stepCount"": { ""$lte"": 1 } 
        } 
    },
    { 
        ""$sort"": { 
            ""businessProcessDefinition"": 1 
        } 
    },
    { 
        ""$limit"": 5 
    },
    { 
        ""$project"": { 
            ""_id"": 0, 
            ""simplropsId"": ""$_id"", 
            ""businessProcessDefinition"": 1, 
            ""stepCount"": 1 
        } 
    }
]
"
3,give me list of business processes last retrieved today based on PST timezone.?,"[\n  {\n    ""$match"": {\n      ""lastRetrievedDate"": {\n        ""$gte"": ""2024-03-14T00:00:00.000Z"",\n        ""$lt"": ""2024-03-15T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""processLastUsed"": {
                ""$gte"": ""2024-08-13T07:00:00.000Z"",
                ""$lt"": ""2024-08-14T07:00:00.000Z""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
4,Which bps have primary initiator roles?,"[\n  {\n    ""$match"": {\n      ""primaryInitiatorRole"": ""Primary Initiator""\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$businessProcessDefinition"",\n      ""simplropsId"": {\n        ""$first"": ""$simplropsId""\n      },\n      ""businessProcessDefinition"": {\n        ""$first"": ""$businessProcessDefinition""\n      }\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""primaryInitiatorRole"": { ""$exists"": True } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1, ""primaryInitiatorRole"": 1 } } ]
"
5,which business processes have Primary Initiator Role,"[\n  {\n    ""$match"": {\n      ""primaryInitiatorRole"": { ""$exists"": true }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""primaryInitiatorRole"": { ""$exists"": True } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1 } } ]
"
6,Which bps have primary initiator roles?,"[\n  {\n    ""$match"": {\n      ""primaryInitiatorRole"": ""Primary Initiator""\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$businessProcessDefinition"",\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""businessProcessDefinition"": ""$_id"",\n      ""count"": 1,\n      ""_id"": 0\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""primaryInitiatorRole"": { ""$exists"": True } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1 } } ]
"
7,which business processes have Primary Initiator Role,"[\n  {\n    ""$match"": {\n      ""primaryInitiatorRole"": { ""$exists"": true }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""primaryInitiatorRole"": { ""$exists"": true } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1 } } ]
"
8,check if any business process is having 'Rule-Based Security Group' security group type under ad hoc appovals by?,"[\n  {\n    ""$match"": {\n      ""adHocApprovalsBy.securityGroupType"": ""Rule-Based Security Group""\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,data,"
[
    {
        ""$match"": {
            ""isArchive"": {
                ""$ne"": true
            }
        }
    },
    {
        ""$lookup"": {
            ""from"": ""securityGroups"",
            ""let"": {
                ""localSecurityGroupType"": ""$securityGroupTypes.securityGroupType""
            },
            ""pipeline"": [
                {
                    ""$match"": {
                        ""$expr"": {
                            ""$and"": [
                                {
                                    ""$regexMatch"": {
                                        ""input"": ""$securityGroupType"",
                                        ""regex"": ""Rule-Based Security Group"",
                                        ""options"": ""i""
                                    }
                                },
                                {
                                    ""$eq"": [
                                        ""$securityGroupType"",
                                        ""$$localSecurityGroupType""
                                    ]
                                }
                            ]
                        }
                    }
                }
            ],
            ""as"": ""sg""
        }
    },
    {
        ""$match"": {
            ""businessProcessTypesAccess.businessProcessTypesGrantedToSecurityGroupApproveAccess"": true
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
9,Which security group has most number of policies,"[\n  {\n    ""$match"": {\n      ""securityGroupName"": {\n        ""$regex"": "".*""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""numberOfPolicies"": {\n        ""$sum"": ""$numberOfPolicies""\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""numberOfPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 1\n  },\n  {\n    ""$project"": {\n      ""securityGroupName"": ""$_id"",\n      ""numberOfPolicies"": 1\n    }\n  }\n]",securityGroups,data,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""policyCount"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""policyCount"": -1 } },
    { ""$limit"": 1 },
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": ""$_id"", ""policyCount"": 1 } }
]
"
10,Which security group has most number of domain policies?,"[\n  {\n    ""$match"": {\n      ""securityGroupDomains"": {\n        ""$elemMatch"": {\n          ""policyName"": {\n            ""$exists"": true\n          }\n        }\n      }\n    }\n  },\n  {\n    ""$unwind"": ""$securityGroupDomains""\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""totalPolicies"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""totalPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 1\n  },\n  {\n    ""$project"": {\n      ""securityGroupName"": ""$_id"",\n      ""totalPolicies"": 1\n    }\n  }\n]",securityGroups,data,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 1 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
11,Which sgs are inactive,"[\n  {\n    ""$match"": {\n      ""isInactive"": true\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[ { ""$match"": { ""isInactive"": True } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": 1 } } ]
"
12,Which sg have most number of domain policies?,"[\n  {\n    ""$match"": {\n      ""securityGroupDomains"": {\n        ""$exists"": true,\n        ""$not"": {\n          ""$size"": 0\n        }\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1,\n      ""numberOfPolicies"": {\n        ""$size"": ""$securityGroupDomains""\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""numberOfPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 1\n  }\n]",securityGroups,data,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 1 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
13,Which sg are active?,"[\n  {\n    ""$match"": {\n      ""isInactive"": {\n        ""$ne"": true\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",securityGroups,list,"
[ { ""$match"": { ""isInactive"": { ""$ne"": true } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": 1 } } ]
"
14,How many business processes have notifications greater than 10?,"[\n  {\n    ""$match"": {\n      ""notifications"": {\n        ""$exists"": true,\n        ""$not"": {\n          ""$size"": 0\n        }\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1,\n      ""numberOfNotifications"": 1\n    }\n  },\n  {\n    ""$match"": {\n      ""numberOfNotifications"": {\n        ""$gt"": 10\n      }\n    }\n  },\n  {\n    ""$count"": ""businessProcessesWithNotificationsGreaterThan10""\n  }\n]",businessProcessEvents,count,"
[
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""notificationCount"": {
                ""$sum"": {
                    ""$size"": ""$notifications""
                }
            },
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            }
        }
    },
    {
        ""$match"": {
            ""notificationCount"": {
                ""$gt"": 10
            }
        }
    },
    {
        ""$count"": ""count""
    }
]
"
15,"Please provide the list of business processes where the due date is greater than ""2_Days""?","[\n  {\n    ""$match"": {\n      ""dueDate"": { ""$gt"": ""2_Days"" }\n    }\n  },\n  {\n    ""$project"": {\n      ""businessProcessDefinition"": 1,\n      ""simplropsId"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""dueDate"": {
                ""$gt"": {
                    ""$dateAdd"": {
                        ""startDate"": ""$$NOW"",
                        ""unit"": ""day"",
                        ""amount"": 2
                    }
                }
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
16,"How many business processes have ""HR_Administrator"" as the Initiating Security Group?","[\n  {\n    ""$match"": {\n      ""initiatingSecurityGroups.initiatingSecurityGroupName"": {\n        ""$regex"": ""HR_Administrator"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""_id"": 0,\n      ""count"": 1\n    }\n  }\n]",businessProcessEvents,count,"
[
    { 
        ""$match"": { 
            ""initiatingSecurityGroup"": { 
                ""$regex"": ""HR_Administrator"", 
                ""$options"": ""i"" 
            } 
        } 
    },
    { 
        ""$group"": { 
            ""_id"": null, 
            ""count"": { 
                ""$sum"": 1 
            } 
        } 
    },
    { 
        ""$project"": { 
            ""_id"": 0, 
            ""count"": 1 
        } 
    }
]
"
17,"Please provide me with the list of all business processes that should have ""Change_Job"" as the business process type.","[\n  {\n    ""$match"": {\n      ""businessProcessType"": ""Change_Job""\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""businessProcessType"": { ""$regex"": ""Change_Job"", ""$options"": ""i"" } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1 } } ]
"
18,"How many business processes have ""HR_Administrator"" as the Initiating Security Group?","[\n  {\n    ""$match"": {\n      ""initiatingSecurityGroups.initiatingSecurityGroupName"": {\n        ""$regex"": ""HR_Administrator"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""_id"": 0,\n      ""count"": 1\n    }\n  }\n]",businessProcessEvents,count,"
[
    { ""$match"": { ""initiatingSecurityGroup"": { ""$regex"": ""HR_Administrator"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
19,Please provide SGs which are active?,"[\n  {\n    ""$match"": {\n      ""isInactive"": {\n        ""$ne"": true\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"**MongoDB Aggregate Query**: 

[
    { ""$match"": { ""isInactive"": { ""$ne"": True } } },
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": 1 } }
]
"
20,Which security group has most number of policies?,"[\n  {\n    ""$match"": {\n      ""numberOfPolicies"": {\n        ""$gt"": 0\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""totalPolicies"": {\n        ""$sum"": ""$numberOfPolicies""\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""totalPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 1\n  },\n  {\n    ""$project"": {\n      ""securityGroupName"": ""$_id"",\n      ""totalPolicies"": 1\n    }\n  }\n]",securityGroups,data,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""policyCount"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } } } },
    { ""$sort"": { ""policyCount"": -1 } },
    { ""$limit"": 1 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""policyCount"": 1 } }
]
"
21,"How many business processes have ""HR_Administrator"" as the Initiating Security Group?","[\n  {\n    ""$match"": {\n      ""initiatingSecurityGroups.initiatingSecurityGroupName"": {\n        ""$regex"": ""HR_Administrator"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""_id"": 0,\n      ""count"": 1\n    }\n  }\n]",businessProcessEvents,count,"
[
    { ""$match"": { ""initiatingSecurityGroup"": { ""$regex"": ""HR_Administrator"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
22,"Please provide me with the list of all business processes that should have ""Change_Job"" as the business process type?","[\n  {\n    ""$match"": {\n      ""businessProcessType"": ""Change_Job""\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""businessProcessType"": {
                ""$regex"": ""Change_Job"",
                ""$options"": ""i""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
23,Get me list of top 5 BPs?,"[\n  {\n    ""$match"": {\n      ""isArchive"": false\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$simplropsId"",\n      ""businessProcessDefinition"": {\n        ""$first"": ""$businessProcessDefinition""\n      },\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""count"": -1\n    }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": ""$_id"",\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    { 
        ""$group"": { 
            ""_id"": ""$businessProcessDefinition"", 
            ""eventCount"": { ""$sum"": 1 }, 
            ""simplropsId"": { ""$first"": ""$simplropsId"" } 
        } 
    },
    { 
        ""$sort"": { ""eventCount"": -1 } 
    },
    { 
        ""$limit"": 5 
    },
    { 
        ""$project"": { 
            ""_id"": 0, 
            ""simplropsId"": 1, 
            ""businessProcessDefinition"": ""$_id"", 
            ""eventCount"": 1 
        } 
    }
]
"
24,give me list of business processes retrieved last month?,"[\n  {\n    ""$match"": {\n      ""lastRetrievedDate"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$match"": { ""processLastUsed"": { ""$gte"": ""2024-07-01T00:00:00.000Z"", ""$lt"": ""2024-08-01T00:00:00.000Z"" } } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""businessProcessDefinition"": 1 } } ]
"
25,give me sgs which has been changed in last month?,"[\n  {\n    ""$match"": {\n      ""lastFunctionallyUpdated"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-07-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-01T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""securityGroupName"": {
                ""$first"": ""$securityGroupName""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""securityGroupName"": 1
        }
    }
]
"
26,How many bps has been changed in last month?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",businessProcessEvents,count,"
[ { ""$match"": { ""definitionLastUpdated"": { ""$gte"": ""2024-07-01T00:00:00.000Z"", ""$lt"": ""2024-08-01T00:00:00.000Z"" }, ""versionCount"": { ""$gt"": 0 } } }, { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
27,give me the list of processes that changed in last 7 days?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-03-14T00:00:00.000Z"",\n        ""$lt"": ""2024-03-21T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            },
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
28,Give me list of processes that were updated in the last 7 days?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-03-14T00:00:00.000Z"",\n        ""$lt"": ""2024-03-21T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""lastFunctionallyUpdated"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
29,How many bps has been retrived last week?,"[\n    {\n        ""$match"": {\n            ""lastRetrievedDate"": {\n                ""$gte"": ""2024-03-21T00:00:00.000Z"",\n                ""$lt"": ""2024-03-28T00:00:00.000Z""\n            }\n        }\n    },\n    {\n        ""$group"": {\n            ""_id"": null,\n            ""totalRetrieved"": {\n                ""$sum"": 1\n            }\n        }\n    }\n]",businessProcessEvents,count,"
[
    {
        ""$match"": {
            ""processLastUsed"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": null,
            ""count"": {
                ""$sum"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""count"": 1
        }
    }
]
"
30,Give me bps has been changed in last month?,"[\n    {\n        ""$match"": {\n            ""definitionLastUpdated"": {\n                ""$gte"": ""2024-02-01T00:00:00.000Z"",\n                ""$lt"": ""2024-03-01T00:00:00.000Z""\n            },\n            ""versionCount"": { ""$gt"": 0 }\n        }\n    },\n    {\n        ""$project"": {\n            ""simplropsId"": 1,\n            ""businessProcessDefinition"": 1\n        }\n    }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-07-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-01T00:00:00.000Z""
            }
        }
    },
    {
        ""$match"": {
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
31,Give me bps has been changed in week?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-03-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-08T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    { 
        ""$match"": { 
            ""definitionLastUpdated"": { 
                ""$gte"": ""2024-08-07T00:00:00.000Z"", 
                ""$lt"": ""2024-08-14T00:00:00.000Z"" 
            } 
        } 
    },
    { 
        ""$match"": { 
            ""versionCount"": { 
                ""$gt"": 0 
            } 
        } 
    },
    { 
        ""$project"": { 
            ""_id"": 0, 
            ""simplropsId"": 1, 
            ""businessProcessDefinition"": 1 
        } 
    }
]
"
32,Give me bps has been changed scince 1st march?,"[\n    {\n        ""$match"": {\n            ""definitionLastUpdated"": {\n                ""$gte"": ""2024-03-01T00:00:00.000Z""\n            }\n        }\n    },\n    {\n        ""$project"": {\n            ""simplropsId"": 1,\n            ""businessProcessDefinition"": 1,\n            ""versionCount"": 1\n        }\n    },\n    {\n        ""$match"": {\n            ""versionCount"": {\n                ""$gt"": 0\n            }\n        }\n    }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-03-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            },
            ""versionCount"": {
                ""$first"": ""$versionCount""
            }
        }
    },
    {
        ""$match"": {
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
33,Give me top 5 sgs which has most mumber of policies?,"[\n  {\n    ""$sort"": {\n      ""numberOfPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1,\n      ""numberOfPolicies"": 1\n    }\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
34,How many bps has been changed in last week?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-03-21T00:00:00.000Z"",\n        ""$lt"": ""2024-03-28T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",businessProcessEvents,count,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            },
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$group"": {
            ""_id"": null,
            ""count"": {
                ""$sum"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""count"": 1
        }
    }
]
"
35,give me bps has been changed in last week?,"[\n  {\n    ""$match"": {\n      ""definitionLastUpdated"": {\n        ""$gte"": ""2024-03-21T00:00:00.000Z"",\n        ""$lt"": ""2024-03-28T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            },
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
36,give me bps has been changed since 1st march?,"[\n    {\n        ""$match"": {\n            ""definitionLastUpdated"": {\n                ""$gte"": ""2024-03-01T00:00:00.000Z""\n            }\n        }\n    },\n    {\n        ""$project"": {\n            ""simplropsId"": 1,\n            ""businessProcessDefinition"": 1,\n            ""versionCount"": 1\n        }\n    }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-03-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": ""$_id"",
            ""businessProcessDefinition"": 1
        }
    }
]
"
37,give me bps has been changed in last week?,"[\n    {\n        ""$match"": {\n            ""definitionLastUpdated"": {\n                ""$gte"": ""2024-03-21T00:00:00.000Z"",\n                ""$lt"": ""2024-03-28T00:00:00.000Z""\n            },\n            ""versionCount"": {\n                ""$gt"": 0\n            }\n        }\n    },\n    {\n        ""$project"": {\n            ""simplropsId"": 1,\n            ""businessProcessDefinition"": 1,\n            ""versionCount"": 1\n        }\n    }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            },
            ""versionCount"": {
                ""$gt"": 0
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""businessProcessDefinition"": 1
        }
    }
]
"
38,give me bps has been changed in 3 days?,"[\n    {\n        ""$match"": {\n            ""definitionLastUpdated"": {\n                ""$gte"": ""2024-03-24T00:00:00.000Z"",\n                ""$lt"": ""2024-03-27T00:00:00.000Z""\n            },\n            ""versionCount"": {\n                ""$gt"": 0\n            }\n        }\n    },\n    {\n        ""$project"": {\n            ""simplropsId"": 1,\n            ""businessProcessDefinition"": 1\n        }\n    }\n]",businessProcessEvents,list,"
[
    {
        ""$match"": {
            ""definitionLastUpdated"": {
                ""$gte"": ""2024-08-11T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": ""$simplropsId"",
            ""businessProcessDefinition"": {
                ""$first"": ""$businessProcessDefinition""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": ""$_id"",
            ""businessProcessDefinition"": 1
        }
    }
]
"
39,tell me something about simplrops?,"<p><b>SimplrOps Overview:</b> Maximizing Workday Efficiency and Optimization</p>\n<ul>\n<li>SimplrOps stands at the forefront of cloud-based configuration management systems, providing organizations with a robust platform designed to streamline Workday execution. With its suite of features tailored to automate manual processes, ensure security compliance, manage risk, optimize configurations, and facilitate efficient multi-tenant management, SimplrOps empowers organizations to harness the full potential of their Workday investment.</li>\n</ul>\n<p><b>Introduction to SimplrOps:</b></p>\n<ul>\n<li>In today's rapidly evolving business landscape, organizations increasingly rely on cloud-based enterprise resource planning (ERP) systems like Workday to streamline operations, enhance productivity, and drive growth. Workday serves as a cornerstone for organizations seeking to modernize their business processes, streamline HR and finance operations, and adapt to changing market dynamics. However, managing and optimizing a Workday environment can pose significant challenges, particularly as organizations scale and evolve over time.</li>\n<li>Enter SimplrOps â€“ a game-changing solution designed to simplify and optimize Workday execution. Developed by a team of industry experts with deep expertise in Workday configuration management and cloud technologies, SimplrOps offers organizations a comprehensive platform to automate manual processes, ensure security access, manage risk, optimize configurations, and facilitate efficient multi-tenant management.</li>\n</ul>\n<p><b>Key Features:</b></p>\n<ul style=""list-style-type:disc;"">\n<li><b>Centralize Workday Configuration Management:</b>\n<ul>\n<li>Provides a comprehensive view across all Workday tenants.</li>\n<li>Streamlines configuration migration and validation processes.</li>\n<li>Facilitates centralized management for increased visibility.</li>\n</ul>\n</li>\n<li><b>Stay on Top of Configuration Changes Over Time:</b>\n<ul>\n<li>Stores and views configuration versions.</li>\n<li>Offers insights to enhance team efficiency.</li>\n<li>Enables tracking of configuration changes over time.</li>\n</ul>\n</li>\n<li><b>Ensure Workday Security is Up to Standard:</b>\n<ul>\n<li>Runs security analysis to identify potential threats.</li>\n<li>Provides visibility required by compliance teams.</li>\n<li>Offers detailed security matrices for complete setup analysis.</li>\n</ul>\n</li>\n<li><b>Feature Adoption:</b>\n<ul>\n<li>Reduces effort and increases product adoption",operationStatistics,data,"To generate a MongoDB aggregate query based on your request, I need to know the specific information you are looking for regarding ""operationStatistics"" in SimplrOps. Please provide more details or clarify your request."
40,How many integrations has been retrieved last week?,"[\n  {\n    ""$match"": {\n      ""lastRetrievedDate"": {\n        ""$gte"": ""2024-03-20T00:00:00.000Z"",\n        ""$lt"": ""2024-03-27T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""_id"": 0,\n      ""count"": 1\n    }\n  }\n]",integrations,count,"
[
    {
        ""$match"": {
            ""isArchive"": {
                ""$ne"": true
            }
        }
    },
    {
        ""$match"": {
            ""retrievedDate"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": null,
            ""count"": {
                ""$sum"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""count"": 1
        }
    }
]
"
41,How many integrations were retrieved last week?,"[\n  {\n    ""$match"": {\n      ""lastRetrievedDate"": {\n        ""$gte"": ""2024-03-20T00:00:00.000Z"",\n        ""$lt"": ""2024-03-27T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",integrations,count,"
[
    {
        ""$match"": {
            ""isArchive"": {
                ""$ne"": True
            }
        }
    },
    {
        ""$match"": {
            ""retrievedDate"": {
                ""$gte"": ""2024-08-07T00:00:00.000Z"",
                ""$lt"": ""2024-08-14T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": null,
            ""count"": {
                ""$sum"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""count"": 1
        }
    }
]
"
42,How many integrations were retrieved last month?,"[\n  {\n    ""$match"": {\n      ""lastRetrievedDate"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",integrations,count,"
[
    {
        ""$match"": {
            ""retrievalDate"": {
                ""$gte"": ""2024-07-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-01T00:00:00.000Z""
            }
        }
    },
    {
        ""$group"": {
            ""_id"": null,
            ""count"": {
                ""$sum"": 1
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""count"": 1
        }
    }
]
"
43,What are the top 5 template types of integrations we have?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$technologyType"",\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  },\n  {\n    ""$limit"": 5\n  }\n]",integrations,list,"
[
    { ""$group"": { ""_id"": ""$technologyType"", ""count"": { ""$sum"": 1 } } },
    { ""$sort"": { ""count"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""templateType"": ""$_id"", ""count"": 1, ""_id"": 0 } }
]
"
44,What are the different bp types we have?,"[\n  {\n    ""$match"": {\n      ""businessProcessType"": {\n        ""$regex"": "".*""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$businessProcessType"",\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""count"": -1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$group"": { ""_id"": ""$businessProcessType"", ""count"": { ""$sum"": 1 } } }, { ""$match"": { ""count"": { ""$gt"": 0 } } }, { ""$project"": { ""businessProcessType"": ""$_id"", ""count"": 1 } }, { ""$sort"": { ""count"": -1 } } ]
"
45,What are the different types of business processes we have?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$businessProcessType"",\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",businessProcessEvents,list,"
[ { ""$group"": { ""_id"": ""$businessProcessType"", ""count"": { ""$sum"": 1 } } }, { ""$match"": { ""count"": { ""$gt"": 0 } } }, { ""$project"": { ""businessProcessType"": ""$_id"", ""count"": 1 } }, { ""$sort"": { ""count"": -1 } } ]
"
46,Provide me with the top 5 security groups where the highest number of policies are assigned.?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""count"": { ""$sum"": ""$numberOfPolicies"" }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
47,Provide me with the top 5 sgs where the highest number of policies are assigned.?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""policyCount"": { ""$sum"": ""$numberOfPolicies"" }\n    }\n  },\n  { ""$sort"": { ""policyCount"": -1 } },\n  { ""$limit"": 5 },\n  {\n    ""$project"": {\n      ""securityGroupName"": ""$_id"",\n      ""policyCount"": 1,\n      ""simplropsId"": 1\n    }\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""policyCount"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""policyCount"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": ""$_id"", ""policyCount"": 1 } }
]
"
48,give me with the top 5 security groups where the highest number of policies are assigned.?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""count"": { ""$sum"": ""$numberOfPolicies"" }\n    }\n  },\n  { ""$sort"": { ""count"": -1 } },\n  { ""$limit"": 5 },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
49,Give me 5 sgs where most number of policies?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$securityGroupName"",\n      ""count"": { ""$sum"": ""$numberOfPolicies"" }\n    }\n  },\n  { ""$sort"": { ""count"": -1 } },\n  { ""$limit"": 5 },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""policyCount"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""policyCount"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": ""$_id"", ""policyCount"": 1 } }
]
"
50,Give me 5 sgs where most number of  domain policies?,"[\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1,\n      ""numberOfPolicies"": 1\n    }\n  },\n  {\n    ""$sort"": {\n      ""numberOfPolicies"": -1\n    }\n  },\n  {\n    ""$limit"": 5\n  }\n]",securityGroups,list,"
[
    { ""$group"": { ""_id"": ""$securityGroupName"", ""numberOfPolicies"": { ""$sum"": { ""$size"": ""$securityGroupDomains"" } }, ""simplropsId"": { ""$first"": ""$simplropsId"" } } },
    { ""$sort"": { ""numberOfPolicies"": -1 } },
    { ""$limit"": 5 },
    { ""$project"": { ""_id"": 0, ""securityGroupName"": ""$_id"", ""numberOfPolicies"": 1, ""simplropsId"": 1 } }
]
"
51,How many policies are there for Accountant Analyst sg?,"[\n  {\n    ""$match"": {\n      ""securityGroupName"": { ""$regex"": ""Accountant Analyst sg"", ""$options"": ""i"" }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1,\n      ""numberOfPolicies"": 1\n    }\n  }\n]",securityGroups,count,"
[
    { ""$match"": { ""securityGroupName"": { ""$regex"": ""Accountant Analyst"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""policyCount"": { ""$sum"": { ""$cond"": { ""if"": { ""$isArray"": ""$securityGroupDomains"" }, ""then"": { ""$size"": ""$securityGroupDomains"" }, ""else"": 0 } } } } },
    { ""$project"": { ""_id"": 0, ""policyCount"": 1 } }
]
"
52,"How many sgs have ""Setup"" domain policies?","[\n  {\n    ""$match"": {\n      ""securityGroupDomains.policyName"": ""Setup""\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,count,"
[
    { ""$match"": { ""securityGroupDomains.policyName"": { ""$regex"": ""Setup"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
53,"How many security groups have ""Setup"" domain policies?","[\n  {\n    ""$match"": {\n      ""securityGroupDomains.policyName"": { ""$regex"": ""Setup"", ""$options"": ""i"" }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": { ""$sum"": 1 }\n    }\n  }\n]",securityGroups,count,"
[
    { ""$match"": { ""securityGroupDomains.policyName"": { ""$regex"": ""Setup"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
54,"How many security groups have policies related to the ""Setup"" domain?","[\n  {\n    ""$match"": {\n      ""securityGroupDomains"": {\n        ""$elemMatch"": {\n          ""functionalArea"": {\n            ""$elemMatch"": {\n              ""policyName"": {\n                ""$regex"": ""Setup"",\n                ""$options"": ""i""\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",securityGroups,count,"
[
    { ""$match"": { ""securityGroupDomains.policyName"": { ""$regex"": ""Setup"", ""$options"": ""i"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
55,Give me active security groups?,"[\n  {\n    ""$match"": {\n      ""isInactive"": { ""$ne"": true }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""securityGroupName"": 1\n    }\n  }\n]",securityGroups,list,"
[ 
    { ""$match"": { ""isInactive"": { ""$ne"": True } } }, 
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""securityGroupName"": 1 } } 
]
"
56,How many security groups are inactive?,"[\n  {\n    ""$match"": {\n      ""isInactive"": true\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""_id"": 0,\n      ""count"": 1\n    }\n  },\n  {\n    ""$sort"": {\n      ""count"": -1\n    }\n  }\n]",securityGroups,count,"
[ { ""$match"": { ""isInactive"": True } }, { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
57,Give me 5 custom reports which has been ran recently?,"[\n  {\n    ""$sort"": { ""lastRunDate"": -1 }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""customReportName"": 1,\n      ""lastRunDate"": 1\n    }\n  }\n]",customReport,list,"
[ { ""$sort"": { ""lastRunDate"": -1 } }, { ""$limit"": 5 }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""customReportName"": 1, ""lastRunDate"": 1 } } ]
"
58,give me with 5 custom reports that have been run recently.?,"[\n  {\n    ""$sort"": {\n      ""lastRunDate"": -1\n    }\n  },\n  {\n    ""$limit"": 5\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""customReportName"": 1,\n      ""lastRunDate"": 1\n    }\n  }\n]",customReport,list,"
[ { ""$sort"": { ""lastRunDate"": -1 } }, { ""$limit"": 5 }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""customReportName"": 1, ""lastRunDate"": 1 } } ]
"
59,What are the diffrent types of leaves we have?,"[\n  {\n    ""$match"": {\n      ""leaveTypeName"": { ""$regex"": "".*"", ""$options"": ""i"" }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$leaveTypeName"",\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",absenceOperation,list,"
[ { ""$group"": { ""_id"": ""$leaveType"", ""count"": { ""$sum"": 1 } } }, { ""$match"": { ""count"": { ""$gt"": 0 } } }, { ""$project"": { ""leaveType"": ""$_id"", ""count"": 1 } }, { ""$sort"": { ""count"": -1 } } ]
"
60,How many top-level organizations are there?,"[\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""totalTopLevelOrganizations"": {\n        ""$sum"": {\n          ""$cond"": [\n            { ""$eq"": [""$organizationLevelFromTop"", 1] },\n            1,\n            0\n          ]\n        }\n      }\n    }\n  }\n]",organizations,count,"
[ { ""$match"": { ""superiorOrgWid"": { ""$exists"": False } } }, { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
61,Give me top-level organizations?,"[\n  {\n    ""$match"": {\n      ""organizationType"": { ""$exists"": true }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$organizationType"",\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",organizations,list,"**MongoDB Aggregate Query**: 

[
    { ""$match"": { ""isArchive"": { ""$ne"": true } } },
    { ""$match"": { ""superiorOrgWid"": { ""$exists"": false } } },
    { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""organizationName"": 1 } }
]
"
62,How manay total health care plan?,"[\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""totalHealthCarePlans"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",benefitsAllHealthCareCoveragePlans,count,"
[ { ""$group"": { ""_id"": null, ""total"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""total"": 1 } } ]
"
63,Give me grade which has maximum pay rang?,"[\n  {\n    ""$group"": {\n      ""_id"": ""$gradeId"",\n      ""maxPayRange"": { ""$max"": ""$payRangeMaximum"" }\n    }\n  },\n  {\n    ""$sort"": { ""maxPayRange"": -1 }\n  },\n  {\n    ""$limit"": 1\n  },\n  {\n    ""$project"": {\n      ""gradeId"": ""$_id"",\n      ""maxPayRange"": 1,\n      ""compensationGradeName"": 1\n    }\n  }\n]",compensationGrade,data,"
[
    { 
        ""$group"": { 
            ""_id"": ""$compensationGrade"", 
            ""maxPayRange"": { ""$max"": ""$payRange"" } 
        } 
    }, 
    { 
        ""$sort"": { ""maxPayRange"": -1 } 
    }, 
    { 
        ""$limit"": 1 
    }, 
    { 
        ""$project"": { ""_id"": 0, ""compensationGrade"": ""$_id"", ""maxPayRange"": 1 } 
    }
]
"
64,Give me grades which accept USD?,"[\n  {\n    ""$match"": {\n      ""payRangeCurrency"": {\n        ""$regex"": ""USD"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""compensationGradeName"": 1\n    }\n  }\n]",compensationGrade,list,"
[ { ""$match"": { ""currency"": ""USD"" } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""compensationGrade"": 1 } } ]
"
65,How many integrations has been changed last month?,"[\n  {\n    ""$match"": {\n      ""lastFunctionallyUpdated"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": { ""$sum"": 1 }\n    }\n  },\n  {\n    ""$sort"": { ""count"": -1 }\n  }\n]",integrations,count,"
[
    { ""$match"": { ""versionCount"": { ""$gt"": 0 }, ""definitionLastUpdated"": { ""$gte"": ""2024-07-01T00:00:00.000Z"", ""$lt"": ""2024-08-01T00:00:00.000Z"" } } },
    { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } },
    { ""$project"": { ""_id"": 0, ""count"": 1 } }
]
"
66,Give me the integrations has been changed last month?,"[\n  {\n    ""$match"": {\n      ""lastFunctionallyUpdated"": {\n        ""$gte"": ""2024-02-01T00:00:00.000Z"",\n        ""$lt"": ""2024-03-01T00:00:00.000Z""\n      },\n      ""versionCount"": { ""$gt"": 0 }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""name"": 1,\n      ""technologyType"": 1\n    }\n  }\n]",integrations,list,"
[
    {
        ""$match"": {
            ""lastFunctionallyUpdated"": {
                ""$gte"": ""2024-07-01T00:00:00.000Z"",
                ""$lt"": ""2024-08-01T00:00:00.000Z""
            }
        }
    },
    {
        ""$project"": {
            ""_id"": 0,
            ""simplropsId"": 1,
            ""NAMEFIELD"": 1
        }
    }
]
"
67,Give me grades which accept USD?,"[\n  {\n    ""$match"": {\n      ""payRangeCurrency"": {\n        ""$regex"": ""USD"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""compensationGradeName"": 1\n    }\n  }\n]",compensationGrade,list,"
[ { ""$match"": { ""currency"": ""USD"" } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""compensationGrade"": 1 } } ]
"
68,How many time off configurations are there?,"[\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",timeOffsOperation,count,"
[ { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
69,How many business process are there?,"[\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",businessProcessEvents,count,"
[ { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
70,What are the different business process types are there?,"[\n  {\n    ""$match"": {\n      ""businessProcessType"": {\n        ""$regex"": "".*"",\n        ""$options"": ""i""\n      }\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": ""$businessProcessType"",\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  },\n  {\n    ""$sort"": {\n      ""count"": -1\n    }\n  }\n]",businessProcessEvents,list,"
[ { ""$group"": { ""_id"": ""$businessProcessType"", ""count"": { ""$sum"": 1 } } }, { ""$sort"": { ""count"": -1 } }, { ""$project"": { ""_id"": 0, ""businessProcessType"": ""$_id"", ""count"": 1 } } ]
"
71,how many retrieve process is in progress?,"[\n  {\n    ""$match"": {\n      ""isRetrieved"": false\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": {\n        ""$sum"": 1\n      }\n    }\n  }\n]",businessProcessEvents,count,"
[ { ""$match"": { ""status"": { ""$regex"": ""In Progress"", ""$options"": ""i"" } } }, { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
72,List of reports with PII report access for business process?,"[\n  {\n    ""$match"": {\n      ""isPII"": true\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""customReportName"": 1\n    }\n  }\n]",customReport,list,"
[ { ""$match"": { ""isPII"": True } }, { ""$project"": { ""_id"": 0, ""simplropsId"": 1, ""customReportName"": 1 } } ]
"
73,How many Business Process Definition in Business Process?,"[\n  {\n    ""$match"": {\n      ""businessProcessDefinition"": { ""$exists"": true, ""$ne"": null }\n    }\n  },\n  {\n    ""$project"": {\n      ""simplropsId"": 1,\n      ""businessProcessDefinition"": 1\n    }\n  },\n  {\n    ""$group"": {\n      ""_id"": null,\n      ""count"": { ""$sum"": 1 }\n    }\n  }\n]",businessProcessEvents,count,"
[ { ""$group"": { ""_id"": null, ""count"": { ""$sum"": 1 } } }, { ""$project"": { ""_id"": 0, ""count"": 1 } } ]
"
