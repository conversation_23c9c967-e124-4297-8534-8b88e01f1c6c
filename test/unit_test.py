import json
import os
import re
import sys

import boto3
import pandas as pd
import redis
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from dotenv import load_dotenv
from openai import OpenAI

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.config import (
    get_ssm_parameters,
    initialize_chain_environment,
    initialize_environment,
)

parameter_names = [
    "openaikey",
    "main-bucket",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
]
(
    client,
    router,
    s3_client,
    bucket_name,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)


model = initialize_chain_environment(parameter_names)


if not os.path.exists("output"):
    os.makedirs("output")


load_dotenv()


# Call the function and get the parameters
parameters = get_ssm_parameters(["openaikey"])
openai_api_key = parameters.get("openaikey")

if not openai_api_key:
    raise ValueError("OpenAI API Key not found in SSM.")

os.environ["OPENAI_API_KEY"] = openai_api_key

redis_client = redis.Redis(host="localhost", port=6379, db=0)

classification_prompt = """
### Task:
You are an intelligent assistant designed to classify user queries into one of three systems: Database, simplrops, or Workday context.If the query pertains to database, classify it as a "database". If the query pertains to simplrops, classify it as a "simplrops". If the query pertains to workday related, classify it as a "workday"

### System Descriptions and Example Queries:

1. **Database**:
  **When user's question needs a data from the database, then you need to classify it as a "database"**
  **Queries related to data storage, retrieval, and database management functions.**
   - Examples:
     - "Show me the sales data for the last quarter."
     - "Retrieve all customer records from the database."
     - "How do I update the price for a specific product category?"
     - "How many job profiles and job families in Jobs and Positions in Workday HCM?"
     - "How many business processes have  "HR_Administrator " as the Initiating Security Group"
     - "give me the list of processes that changed in last 7 days"
     - "give me bps has been changed in last 3 days"
     - "how many items are in integration list give statistics only"
     - "how many items are in integration Archive  give statistics only"
     - "What are the top 5 template types of integrations we have"
     - "What are the different bp types we have"
     - "What are the different types of business processes we have"
     - "Provide me with the top 5 security groups where the highest number of policies are assigned."
     - "Provide me with the top 5 sgs where the highest number of policies are assigned."
     - "give me with the top 5 security groups where the highest number of policies are assigned."
     - "Give me 5 sgs where most number of policies"
     - "Give me 5 sgs where most number of  domain policies"
     - "How many policies are there for Accountant Analyst sg"


**Output:**
{
  "database"
}


2. **simplrops context**:
   **Queries related to simplrops operations, functionalities, and context-specific information Like API url,simplrops details etc.**
   For url or endpoint related and simplrops related details classify as simplrops
   - Examples:
     - "What is simplrops"
     - "Take me to the tenant compare"
     - "How to add and edit in Knowledge library"
     - "What is simplrops"
     - "What is url for sapWorkbook employeeCentral"
     - "How to setup health assessment queue"
     - "How to do tenant data setup"
     - "How do I use the simplrops API to fetch user details?"
     - "What are the steps to initiate a new operation in simplrops?"
     - "Explain how the logging mechanism works in simplrops."
     - "How does simplrops manage risk in Workday environments?"
     - "What optimization features does simplrops provide for Workday tenants?"
     - "How does simplrops support multi-tenant management?"
     - "What insights does simplrops offer to enhance team efficiency?"
     - "How does simplrops facilitate configuration migration and validation?"
     - "What is the role of simplrops in handling Workday's bi-annual new feature releases?"
     - "What can you do?"
     - "Do you have any documentation on how to utilize this feature?"

**Output:**
{
  "simplrops"
}

3. **Workday context**:
   Queries related to Workday operations, HR functions, payroll, employee data, etc.
   - Examples:
     - "How do I check my last month's salary in Workday?"
     - "Show me the list of upcoming holidays in Workday."
     - "How can I update my personal information in Workday?"
     - "How does Workday facilitate learning and development?"
     - "What are the tools provided by Workday for succession planning?"
     - "How does Workday manage employee time tracking and scheduling?"
     - "What are the benefits of using Workday for managing contingent workers?"
     - "How does Workday help in managing workforce compliance?"
     - "What are the capabilities of Workday's business process framework?"
     - "How does Workday ensure high availability and reliability?"
     - "What disaster recovery solutions does Workday offer?"
     - "How does Workday handle data privacy concerns?"
     - "What partnerships and integrations does Workday have with other technology providers?"
     - "How does Workday support industry-specific needs and requirements?"
     - "What certifications and standards does Workday comply with?"

**Output:**
{
  "workday"
}

**Note:**
  - Provide Output as mention in Output Format and provide only in json response without any additional description or symbol

**Input:**
{user_question}
"""

collection_dt_identifier = """ 
Your task is to follow the below instructions:-

#### Step: Database Name and Output Type Extraction
Extract the relevant database name and output type based on the following database models and keywords:

**Database Models:**

**Operation Keywords**
  - **list**: For listing multiple elements or querying a group of records (e.g., "list," "show," "display," "retrieve").
  - **count**: For counting elements or retrieving a number of items (e.g., "count," "how many")


**Instructions**
  - Define output_type as list when a question is asked about multiple elements.
  - Define output_type as data when a question is asked about specific data.
  - Define output_type as count when a question is asked about the number of elements.

**businessProcessEvents**
- Prdict database_name as businessProcessEvents when question is about events, specific actions, states, or changes within a business process.

**integrationRuntimeEvents**
- IntegrationRuntimeEvents track the actions, statuses, and errors related to the operation of an Integration Runtime (IR), such as startups, shutdowns, resource usage, and execution outcomes. This data is critical for monitoring, diagnostics, and auditing of data integration processes.

**businessGroups**
- BusinessGroups represent collections of related entities within an organization, often grouped by function, department, or business unit. They help in organizing resources, managing access, and streamlining operations across different segments of the organization.

**securityGroups**
- SecurityGroups define sets of permissions and access controls for users or systems, determining what resources or actions they can access within a specific environment. They are essential for managing security and ensuring that only authorized entities can interact with sensitive data or functionalities.

**initialsecuritygroups**

**integrations**
- Integrations connect different systems, applications, or data sources, enabling them to work together seamlessly. They facilitate data exchange, process automation, and workflow synchronization across various platforms, ensuring cohesive operation within an ecosystem.

**initialintegrationslists**

**customReport**
- A customReport is a tailored document that presents specific data and insights based on user-defined parameters and requirements. It allows for flexible formatting, data aggregation, and visualization to meet unique business needs, enhancing decision-making and reporting accuracy.

**initialcustomreportslists**
- initialcustomreportslists collection is designed to store user-defined custom reports that cater to specific business needs. Each entry represents a unique report configuration that can be utilized for data analysis and reporting purposes.

**calculatedField**
- A calculatedField is a dynamic field in a data model that derives its value from a formula or expression based on other fields in the dataset. It allows for real-time calculations, such as sums, averages, or complex computations, enhancing data analysis and reporting capabilities.

**initialcalculatedfieldslists**
- initialcalculatedfieldslists collection holds metadata for calculated fields utilized in business processes, facilitating consistent and accurate data analysis.

**absenceOperation**
- AbsenceOperation refers to the processes and actions taken in managing employee absences, including tracking leave requests, approvals, and any associated workflows. This is essential for maintaining accurate attendance records and ensuring compliance with company policies.

**initialAbsenceList**
**initialTimeoffsList**
**timeOffsOperation**
- TimeOffsOperation refers to the management and tracking of employee time-off requests, including vacation, sick leave, and other absences. It encompasses the processes for submitting, approving, and recording time-off requests, ensuring compliance with company policies and accurate leave balances.

**benefitsAllHealthCareCoveragePlans**
- BenefitsAllHealthCareCoveragePlans encompass a range of services provided under various health care coverage options, including preventive care, hospitalization, prescription drugs, mental health services, and specialized treatments. Understanding these benefits helps individuals make informed decisions about their health care needs and coverage choices.

**benefitsInsuranceCoveragePlans**
- BenefitsInsuranceCoveragePlans outline the various insurance options available to employees, detailing the types of coverage, benefits, eligibility criteria, and enrollment procedures. These plans help ensure financial protection and access to necessary healthcare services for employees and their families.

**retrementSavingsPlan**
- A Retirement Savings Plan is a financial strategy that helps individuals save and invest money for their retirement. It typically includes various investment options, tax benefits, and employer contributions, aiming to ensure a stable income during retirement years.

**benefitsSpendingAccountPlans**
- BenefitsSpendingAccountPlans refer to flexible spending accounts that allow employees to allocate pre-tax dollars for eligible expenses such as healthcare, dependent care, and other qualified expenses. These plans help employees manage their healthcare costs effectively while providing tax advantages and promoting financial wellness.

**jobFamilies**
- JobFamilies categorize roles within an organization based on shared characteristics, responsibilities, and skills. They help streamline recruitment, training, and career development by grouping similar positions, such as Technical, Administrative, and Management roles.

**jobProfiles**
- JobProfiles outline the roles, responsibilities, and required skills for specific positions within an organization. They typically include information such as job title, description, qualifications, experience, and performance expectations, serving as a framework for recruitment, training, and employee evaluation.

**compensationGrade**
- CompensationGrade refers to a classification system used to categorize employee salaries based on factors such as job responsibilities, performance levels, and market benchmarks. It helps organizations ensure equitable pay structures and manage compensation strategies effectively.

**compensationPlan**
- A compensation plan outlines the structure of employee remuneration, including salaries, bonuses, benefits, and incentives. It serves as a framework to attract, retain, and motivate employees while aligning with organizational goals and budgets.

**compensationBasis**
- CompensationBasis refers to the foundational criteria or framework used to determine how employees are compensated, including salary structures, bonus eligibility, commission rates, and benefits. It ensures consistency and fairness in remuneration practices within an organization.

**compensationMatrices**
- CompensationMatrices are structured frameworks used to analyze, manage, and visualize compensation data across various roles and departments. They help organizations ensure equitable pay structures, align compensation with performance metrics, and support strategic decision-making in talent management and retention.

**compensationScorecard**
- The Compensation Scorecard is a tool used to evaluate and measure the effectiveness of compensation strategies within an organization. It typically includes metrics such as salary competitiveness, employee performance, retention rates, and alignment with business goals, helping HR professionals make data-driven decisions on compensation planning and adjustments.

**compensationRule**
- A CompensationRule defines the conditions and actions for compensating or rectifying transactions in a business process when certain criteria are met. It ensures that any deviations or failures are addressed effectively, maintaining the integrity and reliability of the overall process.

**compensationPackage**
- A compensation package refers to the total monetary and non-monetary benefits provided to an employee in exchange for their work. It typically includes salary, bonuses, health insurance, retirement contributions, and other perks or allowances designed to attract and retain talent.

**organizations**
- Organizations are structured groups of individuals working together towards common goals or objectives, often defined by shared resources, responsibilities, and a governance framework. They can vary in size, type (such as non-profit, for-profit, governmental), and industry, playing a crucial role in economic and social systems.

**operationCustomFields**
- OperationCustomFields are user-defined fields that allow for the storage of additional metadata or parameters related to specific operations within a system. These fields enhance flexibility and customization, enabling users to tailor operations to their specific needs and track relevant information beyond standard attributes.

**operationDashboards**
- OperationDashboards are visual interfaces that aggregate and display key metrics, performance indicators, and operational data in real-time. They enable users to monitor the health, efficiency, and effectiveness of business processes, facilitating informed decision-making and performance analysis.

**initialDashboard**

**locations**
- Locations refer to specific geographic or virtual places where data, resources, or services are stored or accessed. This can include physical addresses, data centers, cloud regions, or any designated area that supports operational activities.

**rules**
- Rules are specific guidelines or criteria that govern behavior, actions, or processes within a system. They ensure consistency, compliance, and quality by outlining permissible actions, conditions for execution, and expected outcomes.

**jobapplications**
- JobApplications represent the formal submissions made by candidates for job openings within an organization. Each application typically includes the applicant's resume, cover letter, contact information, and relevant qualifications, enabling recruiters to assess and manage the hiring process effectively.

**operationTimeEntryTemplates**
- OperationTimeEntryTemplates define standardized formats for recording time entries related to specific operations or tasks. These templates help ensure consistency in data collection, allowing for accurate tracking, reporting, and analysis of time spent on various activities.

**operationTimeEntryCodes**
- OperationTimeEntryCodes are standardized codes used to categorize and track the time spent on specific operations or tasks within a system. These codes facilitate accurate reporting, analysis, and resource allocation by providing a consistent framework for logging work hours across various activities.

**operationTimeCodeGroups**
- OperationTimeCodeGroups categorize and track the duration of specific operational activities or tasks within a business process. These groups help analyze performance, identify bottlenecks, and optimize resource allocation by providing insights into the time spent on various operations.

**operationPeriodSchedules**
- OperationPeriodSchedules define the specific time frames during which certain operations or tasks are scheduled to occur within a system. This includes details on start and end times, frequency (e.g., daily, weekly), and any conditions that may affect the scheduling of operations.

**operationWorkScheduleCalendars**
- OperationWorkScheduleCalendars are tools used to define and manage the working hours, shifts, and holidays for operations within an organization. They facilitate scheduling by specifying when resources are available for tasks, ensuring optimal workforce utilization and effective planning of operations.

**operationAssetBookRulesCondition**
- OperationAssetBookRulesCondition defines the criteria and rules applied to assets during operations, ensuring compliance with business policies and regulations. This may include conditions for asset allocation, usage restrictions, and validation requirements to maintain operational integrity and accountability.

**operationCompanyBookAsset**
- OperationCompanyBookAsset refers to the management and tracking of assets associated with a company's operational activities. This includes the recording, valuation, and maintenance of physical and intangible assets to ensure accurate financial reporting and compliance with accounting standards.

**operationDepreciationProfiles**
- OperationDepreciationProfiles define the methods and schedules for depreciating the value of assets over time. These profiles outline how different assets lose value based on operational use, allowing for accurate financial reporting and planning within an organization.

**operationBusinessAssets**
- OperationBusinessAssets refer to the resources, tools, and components utilized in the execution of business operations. These assets may include physical resources (like equipment), digital resources (such as software and databases), and human resources (staff and expertise) that are essential for efficient process execution and value delivery.

**operationAssetPoolingRules**
- OperationAssetPoolingRules define the guidelines and criteria for managing and allocating shared assets within operational processes. These rules help optimize resource utilization, minimize costs, and ensure that assets are efficiently pooled and allocated based on demand and availability.

**operationSpendCategory**
- OperationSpendCategory categorizes expenses associated with specific operations or activities within a business. It helps in tracking and analyzing spending patterns, facilitating budget management, and optimizing resource allocation across different operational areas.

**operationCompanies**
- OperationCompanies represent organizations involved in executing business operations or processes. This may include details about their roles, capabilities, locations, and any specific operational parameters relevant to their functions within a larger ecosystem.

**operationLedgerAccount**
- An Operation Ledger Account is a financial record that captures all transactions related to operational activities within an organization. It tracks revenues, expenses, and adjustments, providing insights into the financial performance and enabling accurate reporting and analysis of operational efficiency.

**operationAccountPostingRuleCondition**
- The `operationAccountPostingRuleCondition` specifies the criteria that must be met for a particular operation to trigger the posting of financial entries to the appropriate accounts. This condition ensures that only valid transactions are processed according to predefined rules, helping maintain accuracy and compliance in financial reporting.

**operationFund***
- OperationFund refers to the financial resources allocated for the execution and management of specific operational tasks or projects within an organization. It encompasses budgeting for activities, resource allocation, and financial oversight to ensure efficient operation and achievement of strategic objectives.

**operationAllocationDefinitions**
- OperationAllocationDefinitions specify the allocation of resources or tasks within a given operation. They outline how different resources (e.g., personnel, equipment) are assigned to various tasks, ensuring efficient management and execution of operational workflows.

**operationCurrencyConversionRates**
- OperationCurrencyConversionRates represent the rates used to convert amounts from one currency to another within financial operations. This data is essential for accurate transactions, reporting, and financial analysis, ensuring consistency across different currencies in business processes.

**operationEquityPickupResults**
- OperationEquityPickupResults refers to the data generated during the equity pickup process in operations. It includes metrics and outcomes related to the evaluation of equity positions, performance assessments, and adjustments made to ensure accurate equity reporting. This data is essential for financial analysis and decision-making.

**operationLedgerAccountSummary**
- OperationLedgerAccountSummary provides a consolidated view of financial transactions within a ledger, summarizing key metrics such as total debits, total credits, and account balances over a specified period. It aids in financial analysis, reporting, and reconciliation processes.

**operationRevaluationResults**
- OperationRevaluationResults represent the outcomes and insights generated from reevaluating operational data or processes. This includes metrics, changes in status, performance analysis, and any adjustments made to improve efficiency or effectiveness in operations.

**operationStatistics**
- OperationStatistics refer to the metrics and data points collected during the execution of data operations. This includes details such as execution time, success and failure rates, resource utilization, and data throughput. These statistics are essential for performance analysis, optimization, and monitoring of data workflows.

**operationGrantsManagement**
- OperationGrantsManagement involves overseeing and controlling the allocation and usage of operational grants within an organization. It encompasses processes such as application, approval, monitoring, and reporting of grants, ensuring compliance with regulations and optimizing resource utilization.

**operationAward**
- OperationAward refers to the recognition or reward granted for successful completion of specific operational tasks or projects. It can encompass various forms of acknowledgment, such as bonuses, certificates, or public recognition, aimed at motivating employees and enhancing operational performance.

**operationInventoryLocationAttributes**
- OperationInventoryLocationAttributes define the characteristics and metadata associated with specific inventory locations within an operational context. This may include details such as location ID, type, capacity, current inventory levels, and related operational metrics, enabling effective inventory management and resource allocation.

**operationInventoryPutAwayRules**
- OperationInventoryPutAwayRules specify the guidelines and criteria for efficiently storing received inventory items in a warehouse. These rules optimize space utilization, ensure proper item categorization, and enhance retrieval efficiency, contributing to streamlined inventory management processes.

**operationProcurementRequisition**
- OperationProcurementRequisition refers to the formal request process initiated by an organization to acquire goods or services. This includes details such as item specifications, quantity, required delivery date, and budget considerations. It serves as a critical step in the procurement workflow, ensuring that organizational needs are met efficiently and effectively.

**operationSettlement**
- OperationSettlement refers to the process of finalizing and reconciling transactions or operations within a system, ensuring that all related activities are completed, and any discrepancies are addressed. This includes verifying amounts, updating records, and ensuring that funds or resources are appropriately allocated.

**operationProjects**
- OperationProjects refer to structured initiatives aimed at managing and executing specific operational tasks within an organization. These projects typically encompass planning, resource allocation, execution, and performance tracking to achieve defined business objectives efficiently.

**operationCustomers**
- OperationCustomers represent the clients or users interacting with a specific operational process or service. This data includes customer identifiers, transaction details, interaction timestamps, and feedback, enabling the analysis of customer behavior, satisfaction, and service efficiency.

**operationCustomerContracts**
- OperationCustomerContracts refers to the agreements or contracts established between a business and its customers regarding the terms of service, deliverables, pricing, and other operational details. This data is essential for managing customer relationships, ensuring compliance, and facilitating operational efficiency.

**operationRevenueCategory**
- OperationRevenueCategory refers to the classification of revenue generated from specific business operations or activities. It helps in analyzing financial performance by segmenting revenue streams, allowing for better decision-making and strategic planning.

**operationSuppliers**
- OperationSuppliers refer to the entities or systems that provide the necessary resources, services, or components required to execute business operations. They play a crucial role in ensuring the efficiency and effectiveness of operational processes by delivering timely inputs and support.

**operationSupplierContracts**
- OperationSupplierContracts refer to agreements that outline the terms and conditions between an organization and its suppliers regarding the supply of goods or services. These contracts typically include details such as pricing, delivery schedules, quality requirements, and compliance obligations, ensuring clarity and accountability in the supplier relationship.

**operationCountryTaxRuleLines**
- OperationCountryTaxRuleLines represent the specific tax rules applicable to various countries within an operational context. Each line typically includes details such as the country code, tax rates, applicable dates, and conditions that determine the tax treatment for transactions or operations in that country.

**operationTaxAuthority**
- OperationTaxAuthority refers to the regulatory body responsible for overseeing tax compliance and enforcement within a jurisdiction. This entity manages tax collection, audits, and the implementation of tax laws, ensuring that businesses and individuals adhere to their tax obligations.

**operationTaxRates**
- OperationTaxRates represent the applicable tax rates for various business operations or transactions. This information is crucial for calculating tax liabilities, ensuring compliance with regulations, and maintaining accurate financial records.

**operationPayBalances**
- OperationPayBalances refers to the processes and calculations involved in managing and reconciling payment balances for transactions. This includes tracking outstanding payments, processing payments, and updating financial records to ensure accurate accounting and financial reporting.

**operationEarnings**
- OperationEarnings refer to the total revenue generated from specific operational activities or business functions. This metric helps organizations assess the profitability of their operations, guiding financial planning and performance evaluation.

**operationDeductions**
- OperationDeductions refer to the calculated reductions in resources, costs, or outputs resulting from specific operational activities. These deductions provide insights into efficiency, cost-effectiveness, and areas for improvement within business processes.

**operationPayComponentGroups**
- OperationPayComponentGroups represent the categorization of various payment components involved in financial operations. These groups aggregate different pay elements (like bonuses, deductions, and allowances) to streamline payroll processing, reporting, and compliance within an organization.

**operationPayAccumulations**
- OperationPayAccumulations refer to the cumulative calculations related to payments within a business process. This includes tracking accrued payments, adjustments, and balances over time, which is essential for financial reporting and operational efficiency.

**operationPayGroups**
- OperationPayGroups represent the categorization of payment transactions or payroll operations within a financial system. They group related payment activities, enabling efficient processing, reporting, and management of payroll-related transactions.

**initialbusinessprocesslists**
- The initialbusinessprocesslists collection stores documents related to specific type of business process data, including fields. This collection is used to brief description of purpose or usage.

**initialBusinessAssets**
- talentCertifications stores records of certifications earned by individuals. Each document includes details such as the individual's ID, the certification name, the issuing organization, the issue date, and the status of the certification (e.g., active or expired). It's used to track and manage professional certifications for talents within an organization.

**initialTimeEntryTemplates**
- initialTimeEntryTemplates stores predefined templates for time entries, including details like task description, project information, time duration, and user data. These templates help streamline the process of logging time, ensuring consistency and efficiency in time tracking across different users or tasks.

**InitialJobRequisitionsList**
- InitialJobRequisitionsList collection contains data related to job openings within an organization. Each entry represents a job requisition and typically includes details such as the job title, department, required skills, hiring manager, and the current status of the requisition (e.g., open or closed). This collection helps manage and track job postings and recruitment activities efficiently.

**initialSettlement**
- initialSettlement collection stores financial settlement records between two parties. Each document includes details such as settlement ID, involved parties, amount, currency, settlement date, and status (e.g., pending, completed). It may also track transaction type and associated fees. This data helps manage and track initial financial reconciliations.

**NamingComvention**
- NamingComvention collection stores standardized naming rules to ensure consistency across various resources and processes. Each document defines the resource type, naming pattern, prefixes, suffixes, and specific conditions that guide how names should be structured.

### Task:
Based on the above Database Models, extract the appropriate database name and output type from the user's query and provide the result in the format `{database_name}_{output_type}`.

**Examples:**
1. Prompt:  "List dashboards created or updated within a date range "
  - **Output**: operationDashboards_list
2. Prompt:  "For integration, find the entry from list which has Oracle Fusion in source name "
  - **Output**: integrations_list
3. Prompt:  "Count of dashboards with a menu "
  - **Output**: operationDashboards_count
4. Prompt:  "List of BPs "
  - **Output**: businessGroups_list
5. Prompt:  "get me top 5 custom report types we have "
  - **Output**: customReport_list
6. Prompt:  "Please list all the domain policies that HR admin has access to?"
  - **Output**: securityGroups_list
7. Prompt:  "How many active security groups are there "
  - **Output**: securityGroups_count
8. Prompt:  "what are the permissions for 'Settlement Run Event (Default Definition)' business process "
  - **Output**: businessGroups_data
9. Prompt:  "list of organizations " 
  - **Output**: organizations_list
10. Prompt:  "How many locations are there " 
  - **Output**: locations_list
11. Prompt:  "Show the business process count by type " 
  - **Output**: businessGroups_data
12. Prompt:  "Show the top 5 business processes with the highest step counts " 
  - **Output**: businessGroups_list
13. Prompt:  "How many business processes were used last 7 days " 
  - **Output**: businessGroups_count
14. Prompt:  "Show me the business process with most custom notifications " 
  - **Output**: businessGroups_list
15. Prompt:  "Locations where we have training centers " 
  - **Output**: locations_list
16. Prompt:  "Show me failed integration events for the month of MAY 2024 " 
  - **Output**: integrationRuntimeEvents_list
17. Prompt:  "Please show the security group that has access to rescind permission for "Settlement Run Event (Default Definition)" business process." 
  - **Output**: securityGroups_list
18. Prompt:  "count of integrations retrieved in DCI" 
  - **Output**: initialintegrationslists_count
19. Prompt:  "Business process names whose functional area is staffing" 
  - **Output**: businessGroups_list
20. Prompt: "how much % we retreived into dci"
  - **Output**: initialbusinessprocesslists_count
1. "Show me the sales data for the last quarter."
   - **Output**: operationRevenueCategory_data

2. "Retrieve all customer records from the database."
   - **Output**: operationCustomers_list

3. "How do I update the price for a specific product category?"
   - **Output**: operationSpendCategory_data

4. "How many job profiles and job families in Jobs and Positions in Workday HCM?"
   - **Output**: jobProfiles_count

5. "How many business processes have 'HR_Administrator' as the Initiating Security Group"
   - **Output**: businessProcessEvents_count

6. "give me the list of processes that changed in last 7 days"
   - **Output**: businessProcessEvents_list

7. "give me bps has been changed in last 3 days"
   - **Output**: businessProcessEvents_list

8. "how many allocation definitions are there for financial accounting in workday finanicals?"
   - **Output**: operationAllocationDefinitions_count

9. "How many security groups have 'Setup' domain policies"
   - **Output**: securityGroups_count

10. "how many of versions are available in Integrations"
    - **Output**: integrations_count

11. "Give me with top 5 security groups that have highest number of domain policies"
    - **Output**: securityGroups_list

12. "Business process have Business process definition name is Hire"
    - **Output**: businessProcessEvents_data

13. "can you give me details of the business process definition in business processes which process last used is Mar. 21, 2024 12:00 AM PDT"
    - **Output**: businessProcessEvents_data

14. "how many items are in integration list give statistics only"
    - **Output**: integrations_count

15. "how many items are in integration Archive give statistics only"
    - **Output**: integrations_count

16. "give details of Employee As Self security group"
    - **Output**: securityGroups_data

17. "How many business processes have due date greater than '2_Days'"
    - **Output**: businessProcessEvents_count

18. "How many customers are there in revenue management?"
    - **Output**: operationCustomers_count

19. "How many business processes done not have any notifications"
    - **Output**: businessProcessEvents_count

20. "How many business processes have due date greater than '2_Days'"
    - **Output**: businessProcessEvents_count

21. "Please provide the list of business processes where the due date is greater than '2_Days'."
    - **Output**: businessProcessEvents_list

22. "Please provide the list of business processes that should have the Initiating Security Group Name equal to 'HR_Administrator'."
    - **Output**: businessProcessEvents_list

23. "What are the different types of business processes we have"
    - **Output**: businessProcessEvents_list

24. "Give top 5 business process types"
    - **Output**: businessProcessEvents_list

25. "Give me top 10 processes has been updated recently"
    - **Output**: businessProcessEvents_list

26. "How many business processes are there in the 'Staffing' functional area"
    - **Output**: businessProcessEvents_count

27. "give number of items in the integration"
    - **Output**: integrations_count

28. "give number of items in the integration in which the PII value is 'No'"
    - **Output**: integrations_count

29. "how many integration records having bound type as boomerang"
    - **Output**: integrations_count

30. "Which sg are active? (Sg = Security group)"
    - **Output**: securityGroups_list

31. "Which sg are active?,"
    - **Output**: securityGroups_list

32. "Give me sgs which has been changed in last month,"
    - **Output**: securityGroups_list

33. "What are different integration types are there?,"
    - **Output**: integrations_list

34. "List of security groups with report writer access,"
    - **Output**: securityGroups_list

35. "give the number of items in the integration where the PI value is 'No',"
    - **Output**: integrations_count

36. "Please provide the number of items in the bp where the PI value is 'No',"
    - **Output**: businessProcessEvents_count

37. "What are the source and target names for the integration named AUTO_Change_Jobv35.0?,"
    - **Output**: integrations_data

38. "What is the tenant type for BV EIB Edit Worker Additional Data integration?,"
    - **Output**: integrations_data

39. "which business processes have Primary Initiator Role"
    - **Output**: businessProcessEvents_list

40. "Which bps have primary initiator roles"
    - **Output**: businessProcessEvents_list

41. "which business processes have Primary Initiator Role"
    - **Output**: businessProcessEvents_list

42. "check if any business process is having 'Rule-Based Security Group' security group type under ad hoc appovals by"
    - **Output**: businessProcessEvents_data

43. "Which security group has most number of policies"
    - **Output**: securityGroups_data

44. "Which security group has most number of domain policies"
    - **Output**: securityGroups_data

45. "Which sgs are inactive"
    - **Output**: securityGroups_list

46. "Which sg have most number of domain policies"
    - **Output**: securityGroups_data

47. "Which sg are active"
    - **Output**: securityGroups_list

48. "How many business processes have notifications greater than 10"
    - **Output**: businessProcessEvents_count

49. "Please provide the list of business processes where the due date is greater than '2_Days'"
    - **Output**: businessProcessEvents_list

50. "Please provide me with the list of all business processes that should have 'Change_Job' as the business process type."
    - **Output**: businessProcessEvents_list

51. "Please provide SGs which are active"
    - **Output**: securityGroups_list

52. "Which security group has most number of policies"
    - **Output**: securityGroups_data

53. "Please provide me with the list of all business processes that should have 'Change_Job' as the business process type"
    - **Output**: businessProcessEvents_list

54. "Get me list of top 5 BPs"
    - **Output**: businessProcessEvents_list

55. "give me list of business processes retrieved last month"
    - **Output**: businessProcessEvents_list

56. "give me sgs which has been changed in last month"
    - **Output**: securityGroups_list

57. "How many bps has been changed in last month"
    - **Output**: businessProcessEvents_count

58. "Give me list of processes that were updated in the last 7 days"
    - **Output**: businessProcessEvents_list

59. "How many bps has been retrived last week"
    - **Output**: businessProcessEvents_count

60. "Give me bps has been changed in last month"
    - **Output**: businessProcessEvents_list

61. "Give me bps has been changed in week"
    - **Output**: businessProcessEvents_list

62. "Give me bps has been changed scince 1st march"
    - **Output**: businessProcessEvents_list

63. "Give me top 5 sgs which has most mumber of policies"
    - **Output**: securityGroups_list

64. "How many bps has been changed in last week"
    - **Output**: businessProcessEvents_count

65. "give me bps has been changed in last week"
    - **Output**: businessProcessEvents_list

66. "give me bps has been changed since 1st march"
    - **Output**: businessProcessEvents_list

67. "give me bps has been changed in last week"
    - **Output**: businessProcessEvents_list

68. "give me bps has been changed in 3 days"
    - **Output**: businessProcessEvents_list

69. "tell me something about simplrOps"
    - **Output**: operationStatistics_data

70. "How many integrations has been retrieved last week"
    - **Output**: integrations_count

71. "How many integrations were retrieved last week"
    - **Output**: integrations_count

72. "How many integrations were retrieved last month"
    - **Output**: integrations_count

73. "What are the top 5 template types of integrations we have"
    - **Output**: integrations_list

74. "What are the different bp types we have"
    - **Output**: businessProcessEvents_list

75. "What are the different types of business processes we have"
    - **Output**: businessProcessEvents_list

76. "Provide me with the top 5 security groups where the highest number of policies are assigned."
    - **Output**: securityGroups_list

77. "Provide me with the top 5 sgs where the highest number of policies are assigned."
    - **Output**: securityGroups_list

78. "give me with the top 5 security groups where the highest number of policies are assigned."
    - **Output**: securityGroups_list

79. "Give me 5 sgs where most number of policies"
    - **Output**: securityGroups_list

80. "Give me 5 sgs where most number of domain policies"
    - **Output**: securityGroups_list

81. "How many policies are there for Accountant Analyst sg"
    - **Output**: securityGroups_count

82. "How many sgs have 'Setup' domain policies"
    - **Output**: securityGroups_count

83. "How many security groups have policies related to the 'Setup' domain"
    - **Output**: securityGroups_count

84. "Give me active security groups"
    - **Output**: securityGroups_list

85. "How many security groups are inactive"
    - **Output**: securityGroups_count

86. "Give me 5 custom reports which has been ran recently"
    - **Output**: customReport_list

87. "give me with 5 custom reports that have been run recently."
    - **Output**: customReport_list

88. "What are the diffrent types of leaves we have"
    - **Output**: absenceOperation_list

89. "How many top-level organizations are there"
    - **Output**: organizations_count

90. "Give me top-level organizations"
    - **Output**: organizations_list

91. "How manay total health care plan"
    - **Output**: benefitsAllHealthCareCoveragePlans_count

92. "Give me grade which has maximum pay rang"
    - **Output**: compensationGrade_data

93. "Give me grades which accept USD"
    - **Output**: compensationGrade_list

94. "How many integrations has been changed last month"
    - **Output**: integrations_count

95. "Give me the integrations has been changed last month"
    - **Output**: integrations_list

96. "Give me grades which accept USD"
    - **Output**: compensationGrade_list

97. "How many time off configurations are there"
    - **Output**: timeOffsOperation_count

98. "How many business process are there"
    - **Output**: businessProcessEvents_count

99. "What are the different business process types are there"
    - **Output**: businessProcessEvents_list

100. "how many retrieve process is in progress"
    - **Output**: businessProcessEvents_count

101. "List of reports with PII report access for business process"
    - **Output**: customReport_list

102. "How many Business Process Definition in Business Process"
    - **Output**: businessProcessEvents_count

103. "list of reports that have PII data"
    - **Output**: customReport_list

104. "how many Is PII in business processs"
    - **Output**: businessProcessEvents_count

105. "does business process have PII data in it"
    - **Output**: businessProcessEvents_data

106. "How many version records in BP"
    - **Output**: businessProcessEvents_count

107. "List of condition rules"
    - **Output**: rules_list

108. "Count of integration not retrieved in digital configuration inventory"
    - **Output**: integrations_count

109. "How many release management analyses have been run so far?"
    - **Output**: operationStatistics_count

110. "Please list all the business process permissions that HR administrator has access to?"
    - **Output**: businessProcessEvents_list

111. "List of integrations that have expiring schedules in this month?"
    - **Output**: integrations_list

112. "Count of integrations retrieved in DCI"
    - **Output**: integrations_count

113. "List of integrations that have expiring schedules in July?"
    - **Output**: integrations_list

114. "List of condition rules"
    - **Output**: rules_list

115. "List of condition rules in DCI"
    - **Output**: rules_list

116. "What is the permission count for 'implementers' security groups?"
    - **Output**: securityGroups_count

117. "Show me top 5 business processes with the highest step counts."
    - **Output**: businessProcessEvents_list

118. "List of scheduled integrations that will run in the next 6 hour"
    - **Output**: integrations_list

119. "Which SG has access to ad Hoc approval for the hire (Default Definition) Business process"
    - **Output**: securityGroups_data

120. "How many training centers are located in different places?"
    - **Output**: locations_count

**Note**
  - For questions related to finding percentages or How much data retrived, use an initial supported   collection of data relevant to the question context.

**Output:**
{
  "{database_name}_{output_type}"
}

"""

query_prompt = """
Create MongoDB aggregate queries based on user requests, adhering to the following guidelines:

### step: MongoDB Aggregate Query Guidelines
1. **Stages Order**: Always use stages in the order: `$group`, `$match`, `$project`.
    - Enclose each stage in curly braces .
    - Use a JSON array `[]` to encapsulate the full query.

2. **Filtering and Ordering**: 
    - Use `$match` for filtering documents.
    - Use `$sort` for ordering results, especially when counting occurrences.
    - Apply descending order for count values by default.

3. **Field Inclusion**:
    - Use `$project` to include specific fields in the output.
    - Always include `simplropsId` and `NAMEFIELD` when listing items, unless a specific field value is requested.
    - Always include the `_id` for Integration Event Detail or Integration Runtime Events models only. For all other queries, remove `_id` from the response by including {_id:0} into the $project query.
    - Always include following condition "{ \"$match\": { \"integration\": { \"$exists\": True } } }" and "{ \"$lookup\": { \"from\": \"integrations\", \"localField\": \"integration\", \"foreignField\": \"_id\", \"as\": \"integration\" } }, { \"$unwind\": { \"path\": \"$integration\", \"preserveNullAndEmptyArrays\": False } }" in a proper order to ensure quick response from database for  Integration Event Detail or integrationRuntimeEvents models
    - Replace the integration name with `NAMEFIELD`, group the integrations after unwinding the query, set the first document as the root to remove duplicates, and add a field called `link` with the value 'DCI' when the user requests the status of a specific integration rather than events like a list of failed or completed integrations. The user can also inquire about the status of the same specific integration within a certain timeframe. 

4. **Field and Array Operations**:
    - For counting array elements, use the `$size` operator.
    - Use `$elemMatch` to specify conditions within array elements.
    - Ensure `$size` expects and accepts only numerical values.

5. **Regex and Case Sensitivity**:
    - Use `$regex` with the `i` option for case-insensitive searches.

6. **Dates and Boolean Values**:
    - Convert given dates to UTC 0 (e.g., `Mar. 21, 2024 10:47 PM PDT` to `2024-03-22T05:47:00.000+00:00`).
    - Use `"False"` for `No` and `"True"` for `Yes` in boolean fields.
    - For date ranges like "last week" or "last month", dynamically calculate the date range from the current date.

7. **Changed/Updated Items**:
    - Include a check for `versionCount > 0` when querying for changed or updated items.
    - For `businessGroups`, use `definitionLastUpdated` as the default field and `lastFunctionallyUpdated` as the default field for item updates.

8. **Specific Mappings**:
    - Map specific prompts to fields:
        - "integrations from" → `sourceName`
        - "integrations to" → `targetName`
        - "template type" → `technologyType`

9. **Strict Formatting**:
    - Ensure the output strictly conforms to JSON.stringify format for MongoDB aggregate queries.
    - Avoid additional instructions or information besides the required MongoDB query.

10. **Model Usage**:
    - Refer to the given model schema ('DBSCHEMA') for field names and structure.
    - Follow the examples provided for structure and style but do not replicate them.

11. **Date usage**:
    - Always use ("START DATE", "CURRENT DATE") (Todays date is 14 august 2024) to use date to generate aggregate queries.
    - However you are generating any query, and in that we have a use of ("START DATE", "CURRENT DATE"), always use this one.
    - Do not use dateSubtract and units to calculate the date instead directly put the calculated dates in query using $gt and $lt operators

### Task:
Based on the prompt, generate the MongoDB aggregate query according to the guidelines above.

Generate the final output strictly following JSON formatting rules and ensuring that the output is in the JSON.stringify format.

### Examples
Refer to the given examples solely for context when generating queries,if you find question in these examples use thier output as a query. However, avoid directly replicating these examples to provide the same response:

- Example1: Prompt: Which sg are active?, 
Output: [ { "$match": { "isInactive": { "$ne": True } } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].

- Example2: Prompt: “Show me the top 5 business processes with most approval counts”,
Output: [ { "$group": { "_id": "$businessProcessDefinition", "approvalCount": { "$sum": { "$size": { "$filter": { "input": "$processSteps", "as": "step", "cond": { "$regexMatch": { "input": "$$step.stepType", "regex": "Approval", "options": "i" } } } } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "approvalCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "approvalCount": 1 } } ].

- Example3: Prompt: Give me 5 most recently run custom reports, 
Output: [ { "$sort": { "lastRunDate": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "simplropsId": 1, "customReportName": 1, "lastRunDate": 1 } } ].

- Example4: Prompt: What are different integration types are there?, 
Output: [ { "$match": { "technologyType": { "$regex": ".*" } } }, { "$group": { "_id": "$technologyType", "count": { "$sum": 1 } } } , { "$sort": { "count": -1 } } ].

- Example5: Prompt: How many total calc fields are there, 
Output: [ { "$group": { "_id": null, "total": { "$sum": 1 } } } ].

- Example6: Prompt: List of all time offs with type Vacation, 
Output: [ { "$match": { "timeOffType": { "$regex": "Vacation", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "timeOffName": 1 } } ].

- Example7: Prompt: Give me with top 5 security groups that have highest number of domain policies, 
Output: [ { "$sort": { "numberOfPolicies": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "securityGroupName": 1, "numberOfPolicies": 1, "simplropsId": 1 } } ].

- Example8: Prompt: List of security groups with report writer access, 
Output: [ { "securityGroupDomains": { "$elemMatch": { "policyName": "Custom Report Creation", "permissions.canModify": True} } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].

- Example9: Prompt: Give the number of items in the integration where the PII value is 'No', 
Output: [ { "$match": { "isPII": False } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } } ].

- Example10: Prompt: What are the source and target names for the integration named AUTO_Change_Jobv35.0?, 
Output: [ { "$match": { "name": { "$regex": "AUTO_Change_Jobv35.0", "$options": "i" } } }, { "$project": { "_id": 0, "sourceName": "$sourceName", "targetName": "$targetName" } } ].

- Example11: Prompt: Give me list of functional areas for the Dependent Event for Global Support - JAPAC Group business process, 
Output: [ { "$match": { "businessProcessDefinition": {"$regex": "Dependent Event for Global Support - JAPAC Group", "$options": "i"} } }, { "$project": { "_id": 0, "functionalAreas.functionalAreaName": 1 } } ].

- Example12: Prompt: Please check if there are any security group are duplicated or not, 
Output: [ { "$group": { "_id": "$securityGroupName", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 1 } } }, { "$project": { "securityGroupName": "$_id", "count": 1 } } ].

- Example13: Prompt: “list of all business processes”, 
Output: [{ "$project": { "_id": 0, "simplropsId": 1, NAMEFIELD: 1 } }].

- Example14: Prompt: “What are the different types of security groups we have”, 
Output: [ { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 0 } } }, { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } } ].

- Example15: Prompt: “List all the versions available for "Ready for Hire Business Process Ready For Hire (Default Definition)".”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Accounting Journal Event \\(Default Definition\\)", "$options": "i" } } }, { "$group": { "_id": "$versionCount", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "version": "$_id", "count": 1 } } ].

- Example16: Prompt: “Please list all the permissions for "Hire (Default Definition) Business Process" that an HR administrator has access to.”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Hire \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$permissions" }, { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].

- Example17: Prompt: “List some of the business processes with more than 2 steps”, 
Output: [ { "$group": { "_id": "$simplropsId", "stepCount": { "$sum": { "$size": "$processSteps" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" } } }, { "$match": { "stepCount": { "$gt": 2 } } }, { "$project": { "_id": 0, "simplropsId": "$_id", "businessProcessDefinition": 1, "stepCount": 1 } } ].

- Example18: Prompt: “Please list all the Business Process Permissions that an HR administrator has access to.”, 
Output: [ { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].

- Example19: Prompt: “Please list all the domain policies that an HR administrator has access to.”, 
Output: [ { "$match": { "securityGroupName": { "$regex": "HR Administrator", "$options": "i" } } }, { "$unwind": "$securityGroupDomains" }, { "$group": { "_id": "$securityGroupDomains.policyName", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "policyName": "$_id", "count": 1 } } ].

- Example20: Prompt: “How many tenant Health Assessments have been run so far in SimplrOps for this tenant?”, 
Output: [ { "$match": { "tenantId": `Current tenant id required` } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example21: Prompt: “How many Release management analyses have been run so far?”, 
Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46" } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example22: Prompt: “How many Release management analyses have been run in the last year?”, 
Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46", "startTime": { "$gte": ISODate("2023-07-01T00:00:00.000Z"), "$lt": ISODate("2024-07-01T00:00:00.000Z") } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example23: Prompt: “How many organizations do not have a supervisory organization?, 
Output: [ { "$match": { "isArchive": { "$ne": True } } }, { "$match": { "superiorOrgWid": { "$exists": True } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].

- Example24: Prompt: “List some of the interfaces that have more than 2 steps”, 
Output: [ { "$match": { "numberOfProcessSteps": { "$gt": 2 } } } ].

- Example25: Prompt: “How many training centers are located in different places?”, 
Output: [ { "$group": { "_id": "$locale", "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "locale": "$_id", "count": 1 } } ].

- Example25: Prompt: “List of organizations where employee count is more than 2?”, 
Output: [ { "employeeCount": { "$gt": 2 } } ].

- Example27: Prompt: “What is the latest version of integration in SimplrOps?”, 
Output: [ { "$sort": { "api": -1 } }, { "$project": { "apiVersion": 1, "_id": 0 } }, { "$limit": 1 } ].

- Example28: Prompt: “What reports have PII in them?”, 
Output: [ { "$match": { "isPII": True } }, { "$project": { "customReportName": 1, "_id": 0 } } ].

- Example29: Prompt: “How many Domain security Policies does HR Administrator security group have?”, 
Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$project": { "item": 1, "securityGroupDomainsCount": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } } } ].

- Example30: Prompt: “For HR administrator security group, please list all the Domain Policy accesses it has view and modify access to.?”, 
Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$unwind": { "path": "$securityGroupDomains", "preserveNullAndEmptyArrays": True } }, { "$match": { "$and": [ { "securityGroupDomains.permissions.canView": True }, { "securityGroupDomains.permissions.canModify": True } ] } }, { "$project": { "_id": 0, "policyName": "$securityGroupDomains.policyName", "simplropsId": 1 } } ].

- Example31: Prompt: “What are the different types of security groups we have”, 
Output: [  { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } },  { "$match": { "count": { "$gt": 0 } } },  { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } }]”

- Example32: Prompt: “Show me the business process with most custom notifications”, 
Output: [ { "$group": { "_id": "$simplropsId", "notificationCount": { "$sum": { "$size": "$notifications" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "notificationCount": -1 } }, { "$limit": 1 }, { "$project": { "_id":0, "simplropsId": 1, "businessProcessDefinition": 1, "notificationCount": 1 } } ]”

- Example33: Prompt: “How many business processes has approval steps”, 
Output: [ { $group: { _id: "$simplropsId", approvalStepCount: { $sum: { $size: { $filter: { input: "$processSteps", as: "step", cond: { $regexMatch: { input: "$$step.stepType", regex: "Approval", options: "i" } } } } } }, businessProcessDefinition: { $first: "$businessProcessDefinition" } } }, { $match: { approvalStepCount: { $gt: 0 } } }, { "$count": "count" } ]”

- Example34: Prompt: “Show the top 5pp.services.ollie_service.query_se business processes with the highest step counts”, 
Output: [ { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1 } }]”

- Example35: Prompt: “Show me failed integration events for the month of MAY 2024”, 
Output: [ { "$match": { "integration": { "$exists": True } } }, { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" }, "actualStartDateAndTime": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$project": { "_id": 1, "integrationEventRefId": 1 } } ]” 

- Example36: Prompt: “How many business processes were used last month?”, 
Output: [ { "$match": { "processLastUsed": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "count": 1 } }]” 

- Example19: Prompt: “list of business processes were used last 7 days”, 
Output: [ { "$match": { "processLastUsed": { "$gte": "2024-06-07T00:00:00.000Z", "$lt": "2024-06-14T00:00:00.000Z" } } }, { "$project": { "simplropsId": 1, "businessProcessDefinition": 1 } }]” 

- Example37: Prompt: “list of failed integrations”, 
Output: [ { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$group": { "_id": "$integration._id", "root": { "$first": "$$ROOT" } } }, { "$replaceRoot": { "newRoot": "$root" } }, { "$addFields": { "integrationEventRefId": "$integration.name", "link": "DCI" } }, { "$project": { "link": 1, "simplropsId": 1, "integrationEventRefId": 1 } } ]” 

- Example38: Prompt: “List different process step types of Add Additional Job (Default Definition) business process?”, 
Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Add Additional Job \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$processSteps" }, { "$group": { "_id": "$processSteps.stepType", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "stepType": "$_id", "count": 1 } }]” 

Example 39: Prompt: “Count of integration not retrieved in digital configuration inventory”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { isFullConfigRetrieve: { $ne: 'Yes' } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]

Example 40: Prompt: “How many release management analyses have been run so far?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]

Example 41: Prompt: “Please list all the business process permissions that HR administrator has access to?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $unwind: "$permissions" }, { $group: { _id: "$permissions", count: { $sum: 1 } } }, { $sort: { count: -1 } }, { $project: { permission: "$_id", count: 1 } } ]

Example 42: Prompt: “List of integrations that have expiring schedules in this month?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]

Example 43: Prompt: “Count of integrations retrieved in DCI”, 
Output: [{ "$match": { "tenantId": "CP105_TENANT_ID46", "isArchive": { "$ne": True } } }, { "$count": "matchingDocuments" } ]

Example 44: Prompt: “List of integrations that have expiring schedules in July?”, 
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]

Example 45: Prompt: “List of condition rules”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]

Example 46: Prompt: “List of condition rules in DCI”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]

Example 47: Prompt: “What is the permission count for 'implementers' security groups?”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$match": { "securityGroupName": { "$regex": "implementers", "$options": "i" } } }, { "$group": { "_id": "$securityGroupName", "permissionsCount": { "$sum": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$project": { "_id": 0, "simplropsId": 1, "permissionsCount": 1 } }, { "$group": { "_id": null, "totalPermissionsCount": { "$sum": "$permissionsCount" }, "details": { "$push": { "simplropsId": "$simplropsId", "permissionsCount": "$permissionsCount" } } } }, { "$project": { "_id": 0, "totalPermissionsCount": 1 } } ]

Example 48: Prompt: “Show me top 5 business processes with the highest step counts.”, 
Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1, "_id": 0 } } ]

Example 49: Prompt: "List of scheduled integrations that will run in the next 6 hours."
Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate( ""2024-08-06T00:00:00.000Z"" ), $lt: ISODate( ""2024-08-06T06:00:00.000Z"" ) } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } }]

Example 50: Prompt: “Which SG has access to ad Hoc approval for the hire (Default Definition) Business process?”,
Output: [ { $match: {isArchive: { $ne: True } } }, { $lookup: { from: "businessGroups", let: { localBusinessProcessType: "$businessProcessTypesAccess.businessProcessType" }, pipeline: [ { $match: { $expr: { $and: [ { $regexMatch: { input: "$businessProcessDefination", regex: "Hire \(Default Definition\)", options: "i" } }, { $eq: [ "$businessProcessType", "$$localBusinessProcessType" ] } ] } } } ], as: "bp" } }, { $match: { "businessProcessTypesAccess.businessProcessTypesGrantedToSecurityGroupApproveAccess": True } }, { $project: { _id: 0, simplropsId: 1, securityGroupName: 1 } } ]

Generated Output should include:
1. **MongoDB Aggregate Query**: JSON array `[{ }]` containing the required stages (`$group`, `$match`, `$project`).

**Note**: 
    - Ensure the MongoDB query aligns properly with the identified database collection to achieve expected results without additional text.
    - Generate query according to given schema and query_type
    - query_type is count then generate query that returns count, if query_type is data then generate query that returns single data, If query_type is list then generate query that returns list of details 

here is,
**Input Prompt**: {prompt}
**Query Type**: {query_type}
**Mongo schema**: {schema}
"""

query_match_prompt = """
    Calculate the match percentage between two MongoDB queries based on the similarity 
    of their structure, fields, and values.

    Args:
        query1 (dict): {query1}
        query2 (dict): {query2}

    Returns:
        float: The match percentage between the two queries (0-100 scale).

    **Note**
    - Provide Small Summary for the match and match percentage
    """


file_path = "test_case_data/question_classification_testcase_data.csv"
# Load your test data
test_data = pd.read_csv(file_path)


# Function to get the response
def general_response(question):
    messages = [
        {"role": "system", "content": classification_prompt},
        {"role": "user", "content": question},
    ]
    response = client.chat.completions.create(
        model="simplrops-gpt-4o",
        messages=messages,
        temperature=0.3,
        top_p=0.5,
        max_tokens=1000,
    )
    # Get the response and remove unnecessary characters
    raw_response = response.choices[0].message.content
    clean_response = re.sub(r'[{}"]', "", raw_response)  # Remove `{`, `}`, and `"`
    return clean_response.strip()


def get_query(instructions, question, schema, history):
    messages = [{"role": "system", "content": instructions}]

    for message in history:
        messages.append({"role": "user", "content": message[0]})
        messages.append({"role": "assistant", "content": message[1]})

    messages.append(
        {
            "role": "user",
            "content": [
                {"type": "text", "text": question},
                {"type": "text", "text": schema},
            ],
        }
    )
    response = client.chat.completions.create(
        model="simplrops-gpt-4o",
        messages=messages,
        temperature=0.2,
        max_tokens=256,
        top_p=0.2,
        frequency_penalty=0,
        presence_penalty=0,
        response_format={"type": "text"},
    )
    return response.choices[0].message.content


def match_query(query1, query2):
    messages = [
        {"role": "system", "content": query_match_prompt},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": query1},
                {"type": "text", "text": query2},
            ],
        },
    ]
    response = client.chat.completions.create(
        model="simplrops-gpt-4o",
        messages=messages,
        temperature=0.2,
        max_tokens=256,
        top_p=0.2,
        frequency_penalty=0,
        presence_penalty=0,
    )

    return response.choices[0].message.content


# Iterate over each row in the DataFrame and get the response
for index, row in test_data.iterrows():
    question = row["Questions"]
    try:
        print(f"Processing row {index}: {question}")  # Added print statement
        # Get the model response
        model_response = general_response(question)
        # Update the DataFrame with the cleaned response
        test_data.at[index, "Model_reponse"] = model_response
    except Exception as e:
        print(f"Error processing question at index {index}: {e}")
        test_data.at[index, "Model_reponse"] = "Error"

output_file_path = "output/classification_result.csv"
output_file_path = "output/classification_result.csv"
# Save the updated DataFrame to a new CSV file
test_data.to_csv(output_file_path, index=False)


print("Model responses have been updated.")

# Load your test data
test_data = pd.read_csv(output_file_path)
test_data = pd.read_csv(output_file_path)
# Load your test data
test_data = pd.read_csv(
    output_file_path
)  # Replace with your actual file path")  # Replace with your actual file path


# Function to compare Expected_reponse and Model_reponse
def evaluate_responses(row):
    if row["Expected_reponse"].strip() == row["Model_reponse"].strip():
        return "Pass"
    else:
        return "Fail"


# Apply the evaluation function to each row
test_data["Result"] = test_data.apply(evaluate_responses, axis=1)

# Count the number of passes and fails
pass_count = test_data["Result"].value_counts().get("Pass", 0)
fail_count = test_data["Result"].value_counts().get("Fail", 0)

# Save the updated DataFrame to a new CSV file
test_data.to_csv(output_file_path)  # Replace with your actual file path", index=False)
test_data.to_csv(output_file_path)  # Replace with your actual file path", index=False)

# Print the results

print(f"Total Passed: {pass_count}")
print(f"Total Failed: {fail_count}")

file_path = "test_case_data/collection_datatype_identifier_testcase_data.csv"
test_data = pd.read_csv(file_path)


# Load your test data
test_data = pd.read_csv(file_path)


# Function to get the response
def general_response(question):
    messages = [
        {"role": "system", "content": collection_dt_identifier},
        {"role": "user", "content": question},
    ]
    response = client.chat.completions.create(
        model="simplrops-gpt-4o",
        messages=messages,
        temperature=0.3,
        top_p=0.5,
        max_tokens=1000,
    )
    # Get the response and remove unnecessary characters
    raw_response = response.choices[0].message.content
    clean_response = re.sub(r'[{}"]', "", raw_response)  # Remove `{`, `}`, and `"`
    return clean_response.strip()


# Iterate over each row in the DataFrame and get the response
for index, row in test_data.iterrows():
    question = row["Question"]
    try:
        print(f"Processing row {index}: {question}")  # Added print statement
        # Get the model response
        Model_collection_dt = general_response(question)
        # Update the DataFrame with the cleaned response
        test_data.at[index, "Model_collection_dt"] = Model_collection_dt
    except Exception as e:
        print(f"Error processing question at index {index}: {e}")
        test_data.at[index, "Model_collection_dt"] = "Error"

# Create the new filename by appending "_1" to the original filename
new_file_path = "output/database_identifier_result.csv"
new_file_path = "output/database_identifier_result.csv"

# Save the updated DataFrame to the new CSV file
test_data.to_csv(new_file_path, index=False)
test_data.to_csv(new_file_path, index=False)

print(f"Model responses have been updated and saved to: {new_file_path}")

df = pd.read_csv(new_file_path)
df = pd.read_csv(new_file_path)


# Compare Expected_collection_dt and Model_collection_dt columns
df["Result"] = df.apply(
    lambda row: "Pass"
    if row["Expected_collection_dt"] == row["Model_collection_dt"]
    else "Fail",
    axis=1,
)

# Calculate total passes and fails
total_pass = df["Result"].value_counts().get("Pass", 0)
total_fail = df["Result"].value_counts().get("Fail", 0)
total = len(df)

# Print the summary of pass/fail counts
print(f"Total: {total}")
print(f"Total Passed: {total_pass}")
print(f"Total Failed: {total_fail}")

# Filter out the rows where the result is "Fail"
failed_rows = df[df["Result"] == "Fail"]

# Print the failed rows
print(failed_rows)
df.to_csv(new_file_path)
df.to_csv(new_file_path)

# query generation test

query_data = pd.read_csv("test_case_data/query_testcase_data.csv")
# Convert 'dt' column to string if not already
query_data["dt"] = query_data["dt"].astype(str)

# Separate based on "_" and expand into two columns
query_data[["collection", "query_type"]] = query_data["dt"].str.split(
    "_", n=1, expand=True
)

# Drop the original 'dt' column
query_data.drop("dt", axis=1, inplace=True)
for index, row in query_data.iterrows():
    data = None
    question = row.iloc[0]
    query1 = row.iloc[1]
    database = row.iloc[2]
    with open(f"json/{database}.json", "r") as f:
        data = json.load(f)

    model_query = get_query(query_prompt, question, str(data), [])
    model_query = (
        model_query.replace("`", "")
        .replace("json", "")
        .replace("True", "True")
        .replace("False", "False")
    )
    query_data.loc[index, "Output_query"] = model_query
    match_percentage = (
        match_query(query1=query1, query2=model_query)
        .replace("`", "")
        .replace("json", "")
    )
    query_data.loc[index, "Match_percentage"] = match_percentage


query_data.to_csv("output/query_test_case_output.csv")
