{"_id": "ObjectId", "simplropsId": "String", "tenantId": "String", "organization": "String", "organizationWid": "String", "organizationName": "String", "organizationEffectiveDate": "Date", "organizationType": "String", "organizationDisplayID": "String", "countLevelsWithinOrganization": "Number", "active": "Boolean", "availabilityDate": "Date", "referenceID": "String", "contingentWorkerCount": "Number", "employeeCount": "Number", "contingentWorkerPercentage": "Number", "countOfEmployeesPreviouslyContingentWorker": "Number", "versionCount": "Number", "defaultOrganizationAssignments": "Array", "defaultRoleAssignments": "Array", "employeeCountExempt": "Number", "employeeCountNonExempt": "Number", "employeeCountGreaterThanZero": "Boolean", "employeeCountOnLeave": "Number", "includeManagerInName": "Boolean", "lastFunctionallyUpdated": "Date", "lastRetrievedDate": "Date", "organizationLevelFromTop": "Number", "organizationRoleAssignments": "Array", "organizationRoles": "Array", "subtype": "String", "superiorOrgWid": "String", "superiorOrganizationName": "String", "visibility": "String", "isPII": "Boolean", "isArchive": "Boolean", "recommendations": "Array", "isRecommendations": "String", "isChangesFound": "Boolean", "includedByOrganizations": "Array", "includedOrganizations": "Array", "children": "Array", "defaultRoleAssignmentsGroup": "Array", "orgRoleAssignments": "Array", "createdAt": "Date", "updatedAt": "Date", "__v": "Number"}