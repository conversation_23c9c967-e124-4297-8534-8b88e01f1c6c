{"_id": {"$oid": "str"}, "customReportWID": "str", "tenantId": "str", "IsTemporary": "bool", "accountExpirationDate": "None or str", "accountLockedDisabledExpired": "bool", "accountLockedInvalidCredentials": "bool", "areaWhereUsed": "list", "availableUsage": "list", "countOfInvalidAttemptsSinceLastSuccess": "None or int", "countOfUnsuccessfulAttemptsSinceLastSuccess": "None or int", "createdAt": {"$date": "str"}, "customReportName": "str", "customReportType": "str", "doNotUseDataSources": "list", "doNotUseReportFields": "list", "exclude": "bool", "fieldsDisplayedOnReport": "list", "fieldsReferencedInReport": "list", "hasReportTag": "bool", "isEmbeddedIntelligence": "bool", "isFullConfigRetrieve": "str", "isMobileReport": "bool", "isNewToSimplrops": "bool", "isNotDeletedFromWorkday": "bool", "isPII": "bool", "lastFunctionallyUpdated": "None or str", "licensedProductId": "str", "numberOfTimesExecutedInLastYear": "None or int", "ownerIsImplementer": "bool", "ownerIsIntegrationUser": "bool", "reportAreaWhereUsed": "list", "reportAvailableUsage": "list", "reportContainsAnalyticIndicators": "bool", "reportCreatedOn": "None or str", "reportEnabledAsWebService": "bool", "reportEnabledAsWorklet": "bool", "reportPrompts": "list", "reportTags": "list", "shared": "bool", "sharingGroupsExplicitlySpecified": "list", "sharingUsersExplicitlySpecified": "list", "webSerivceAPIVersion": "None or str", "webServiceEnabled": "bool", "isArchive": "bool", "updatedAt": {"$date": "str"}, "errorMessage": "str", "lastRetrieveDate": {"$date": "str"}, "simplropsId": "str", "acountExpirationDate": "None or str"}