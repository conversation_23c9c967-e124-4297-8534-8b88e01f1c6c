{"simplropsId": {"required": true, "index": true}, "isPII": {"default": false}, "tenantId": {"index": true, "required": true}, "licensedProductId": {"index": true}, "integrationId": {"required": true}, "integrationType": {}, "integrationOwner": {"required": true}, "technologyType": {"required": true}, "name": {"required": true}, "intSysUser": {}, "integrationFilter": {}, "integrationDataSource": {}, "vendorName": {}, "vendorProduct": {}, "existsToday": {}, "tenantType": {}, "integrationSystemWid": {"index": true}, "status": {}, "description": {}, "boundType": {}, "outputFormat": {}, "apiVersion": {}, "businessProcessDependencyType": {}, "transactionDateType": [{}], "averageProcessingTimeHHMSS": {}, "bpInitiatedOnly": {"default": false}, "changeOnly": {"default": false}, "isFromStoyl": {"default": false}, "isOptimized": {"default": false}, "confidential": {"default": false}, "isRequiredStep": {"default": false}, "responseRequiredToMoveForward": {"default": false}, "isGlobal": {"default": false}, "lastRunDateTime": {}, "touchPointCount": {}, "countries": [{}], "webService": [{"Web_Service_Name": {}, "Web_Service_Operation_Name": {}, "Web_Service_Fields": {}}], "delivery": [{"method": {}, "endpoint": {}, "isCompressed": {}, "documentRetentionPolicy": {}, "encryptionType": {}, "keyName": {}, "authentication": {}, "format": {}, "environments": [{}]}], "integrationServices": [{"name": {}, "enabled": {"default": false}}], "attributes": [{"provider": {}, "name": {"required": true}, "desc": {}, "valueData": [{"value": {}, "environmentReference": {}}], "isRequired": {"default": false}}], "maps": [{"provider": {}, "name": {"required": true}, "desc": {}, "defaultValue": {}, "mapValues": [{"internal": {}, "external": {}}]}], "dataFields": [{"businessObjectName": {}, "fieldName": {}, "wid": {"index": true}, "deliveredOrCalculated": {}, "fieldType": {}}], "scheduleLastFunctionallyUpdateDate": {}, "schedule": [{"scheduleName": {}, "schedulerWID": {}, "isActive": {"default": false}, "scheduledProcess": {}, "integrationSystemWID": {}, "status": {}, "frequencyType": {}, "timeZone": {}, "startDate": {}, "endDate": {}, "restrictedToEnvironment": {}, "processType": {}, "lastRunDurationTime": {}, "isExpired": {}, "numberOfTimesRun": {}, "recurrenceIntervalAsMinutes": {}, "isEveryWeekdayForDaily": {}, "isEveryMonthForMonthly": {}, "monthsOfYearForMonthly": [{}], "daysOfMonthForMonthly": [{}], "daysOfWeekForWeekly": [{}], "dayOfWeekForMonthly": {}, "scheduledOwnerIsIntSysUser": {}, "nextScheduledDateTime": {}, "hourlyFrequency": {"recurringInterval": {}, "startTime": {}}, "dailyFrequency": {"recursEveryWeekday": {"default": false}, "recursEveryXDays": {}, "startTime": {}}, "weeklyFrequency": {"recursEveryXWeeks": {}, "daysSelected": [{}], "startTime": {}}, "monthlyFrequency": {"months": {"eachMonth": {"default": false}, "monthsSelected": [{}]}, "recurrenceType": {"monthWise": {"default": false}, "datesOfTheMonth": [{}], "daysOfTheWeek": {}, "isLastInMonth": {"default": false}, "onDayOfTheWeek": {}}, "startTime": {}}}], "exceptions": [{"status": {}, "message": {}}], "launchParameters": [{"fieldName": {}, "valueType": {}, "value": [{}]}], "integrationNotification": [{"triggerOnStatus": {}, "triggerOnLaunch": {}}], "workdayFields": {"service": {"serviceId": {}, "serviceName": {}, "operation": {"operationIds": [{}], "operations": {}, "fields": {}}}}, "source": {"ref": "customerProducts", "required": true}, "sourceName": {}, "target": {"ref": "customerProducts", "required": true}, "targetName": {}, "pastRuntimeEventsCounts": {"default": 0}, "versionCount": {"default": 0, "index": true}, "securityLastFunctionallyUpdateDate": {}, "securityDetails": {"securityAccountName": {}, "isIntSysUser": {"default": false}, "securityGroups": [{"ref": "securityGroups"}]}, "integrationDataMapping": {"ref": "integrationDataMapping"}, "runtimeEventDetails": {"count": {"default": 0}, "lastRuntimeEvent": {"ref": "integrationRuntimeEvents"}, "pastRuntimeEvents": [{"ref": "integrationRuntimeEvents"}]}, "integrationStatuses": {"ref": "integrationStatuses"}, "tags": [{"ref": "integrationTags"}], "reportLastFunctionallyUpdateDate": {}, "customReports": [{"ref": "productReports"}], "integrationPlans": [{"ref": "integrationPlans"}], "subscribedBusinessProcessTypes": [{"ref": "businessProcessTypes"}], "plans": [{"ref": "plans"}], "stoylRecommendations": [{"ref": "stoylRecommendations"}], "updatesFound": [{"tenantName": {}, "updatedField": {"ref": "workdayCalculatedFields"}, "date": {"default": "2024-07-30T06:01:01.261Z"}, "isReviewed": {"default": false}}], "resources": [{"resource": {"ref": "resources"}, "primaryResource": {"default": false}}], "dependency": [{"ref": "integrations"}], "lastFunctionallyUpdated": {}, "lastRetrievedDate": {"default": "2024-07-30T06:01:01.261Z"}, "transferType": {}, "isSubscribedToAllBusinessProcesses": {"default": false}, "isSubscribedToAllTransactionTypes": {"default": false}, "doesExcludeTransactionLogTypes": {"default": false}, "doesExcludeBusinessProcessTypes": {"default": false}, "isSubscribedToSpecificTransactionTypes": {"default": false}, "isRaaSOnly": {"default": false}, "sgDetails": [{"securityGroupWid": {}, "securityLastUpdated": {}}], "PGPKeys": [{"ref": "publicPGPKeys"}]}