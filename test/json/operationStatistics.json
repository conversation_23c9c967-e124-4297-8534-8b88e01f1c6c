{"_id": {"type": "ObjectId"}, "tenantId": {"type": "String"}, "isPII": {"type": "Boolean"}, "simplropsId": {"type": "String"}, "lastRetrievedDate": {"type": "Date"}, "isChangesFound": {"type": "Boolean"}, "isRecommendations": {"type": "String"}, "isArchive": {"type": "Boolean"}, "recommendations": {"type": "Array", "items": {"type": "String"}}, "statistic": {"type": "String"}, "workdayID": {"type": "String"}, "referenceID": {"type": "String"}, "fiscalPeriod": {"type": "String"}, "statisticDefinition": {"type": "String"}, "statisticDefinitionDescription": {"type": "String"}, "statisticType": {"type": "String"}, "allocationDefinitionsUsingStatistic": {"type": "Array", "items": {"type": "Mixed"}}, "companiesUsingStatistic": {"type": "Array", "items": {"type": "String"}}, "financialAttachments": {"type": "Array", "items": {"type": "Mixed"}}, "fiscalYearsForFiscalPeriodOfStatistic": {"type": "Array", "items": {"type": "String"}}, "ledgerPlanStructure": {"type": "Array", "items": {"type": "String"}}, "requiredDimensions": {"type": "Array", "items": {"type": "String"}}, "statisticLine": {"type": "Array", "items": {"type": "String"}}, "lastFunctionallyUpdatedDate": {"type": "Date"}, "companyIsIncluded": {"type": "Boolean"}, "isInactive": {"type": "Boolean"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "__v": {"type": "Number"}}