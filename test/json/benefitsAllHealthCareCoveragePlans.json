{"type": "object", "properties": {"_id": {"type": "object", "properties": {"$oid": {"type": "string"}}, "required": ["$oid"]}, "simplropsId": {"type": "string"}, "tenantId": {"type": "string"}, "healthCareClassification": {"type": "string"}, "healthCareCoveragePlanName": {"type": "string"}, "benefitProvider": {"type": "string"}, "referenceID": {"type": "string"}, "frequency": {"type": "string"}, "requiresProviderID": {"type": "boolean"}, "workdayID": {"type": "string"}, "currency": {"type": "string"}, "isPII": {"type": "boolean"}, "isArchive": {"type": "boolean"}, "isChangesFound": {"type": "boolean"}, "isACAMinimumEssentialCoverageForDependents": {"type": "boolean"}, "isACAMinimumValueCoverage": {"type": "boolean"}, "hasSpousalCoverageOfferedConditionally": {"type": "boolean"}, "isACAMinimumEssentialCoverageForEmployee": {"type": "boolean"}, "isAutoEnroll": {"type": "boolean"}, "isACAMinimumEssentialCoverageForSpouse": {"type": "boolean"}, "lastFunctionallyUpdatedDate": {"type": "object", "properties": {"$date": {"type": "string", "format": "date-time"}}, "required": ["$date"]}, "lastRetrievedDate": {"type": "object", "properties": {"$date": {"type": "string", "format": "date-time"}}, "required": ["$date"]}, "recommendations": {"type": "array", "items": {}}, "isRecommendations": {"type": "string"}, "benefitPlanYearDefinition": {"type": "array", "items": {"type": "string"}}, "payComponent": {"type": "array", "items": {"type": "string"}}, "healthCareCoverageCostForHealthCareCoveragePlan": {"type": "array", "items": {"type": "string"}}, "versionCount": {"type": "integer"}, "payrollCostAmount": {"type": "number"}, "maximumContributionAmount": {"type": "number"}, "minimumContributionAmount": {"type": "number"}, "createdAt": {"type": "object", "properties": {"$date": {"type": "string", "format": "date-time"}}, "required": ["$date"]}, "updatedAt": {"type": "object", "properties": {"$date": {"type": "string", "format": "date-time"}}, "required": ["$date"]}, "__v": {"type": "integer"}}, "required": ["_id", "simplropsId", "tenantId", "healthCareClassification", "healthCareCoveragePlanName", "benefitProvider", "referenceID", "frequency", "requiresProviderID", "workdayID", "currency", "isPII", "isArchive", "isChangesFound", "isACAMinimumEssentialCoverageForDependents", "isACAMinimumValueCoverage", "hasSpousalCoverageOfferedConditionally", "isACAMinimumEssentialCoverageForEmployee", "isAutoEnroll", "isACAMinimumEssentialCoverageForSpouse", "lastFunctionallyUpdatedDate", "lastRetrievedDate", "recommendations", "isRecommendations", "benefitPlanYearDefinition", "payComponent", "healthCareCoverageCostForHealthCareCoveragePlan", "versionCount", "payrollCostAmount", "maximumContributionAmount", "minimumContributionAmount", "createdAt", "updatedAt", "__v"]}