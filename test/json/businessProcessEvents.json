{"_id": {"type": "ObjectId"}, "actionEventWID": {"type": "String"}, "processDefinitionWID": {"type": "String", "nullable": true}, "tenantId": {"type": "String"}, "actionEvent": {"type": "String"}, "adHocApprovalCounts": {"type": "Integer"}, "businessProcessDefinition": {"type": "String", "nullable": true}, "businessProcessType": {"type": "String"}, "createdAt": {"type": "Date"}, "dateEffective": {"type": "Date"}, "dateInitiated": {"type": "Date"}, "dateTimeCompleted": {"type": "Date"}, "definitionLastUpdated": {"type": "String", "nullable": true}, "eventIsOnHold": {"type": "Boolean"}, "eventStatus": {"type": "String"}, "futureDatedTransaction": {"type": "Boolean"}, "hasServiceStep": {"type": "Boolean"}, "isCorrected": {"type": "Boolean"}, "isInitiatedByHRAdmin": {"type": "Boolean"}, "isInitiatedByManager": {"type": "Boolean"}, "isInitiatedByServiceCenterRep": {"type": "Boolean"}, "isInitiatedByWorker": {"type": "Boolean"}, "isLoadedViaAPI": {"type": "Boolean"}, "isStandAloneProcess": {"type": "Boolean"}, "lastFunctionallyUpdated": {"type": "Date"}, "nameOfCountry": {"type": "String"}, "numberOfDaysToComplete": {"type": "Integer"}, "numberOfNotifications": {"type": "String"}, "numberOfProcessSteps": {"type": "Integer"}, "overallBusinessProcessType": {"type": "String"}, "percentTaskComplete": {"type": "Integer"}, "processCycleTimeDays": {"type": "Integer"}, "processLastUsed": {"type": "String", "nullable": true}, "processReason": {"type": "String"}, "processReasonCategory": {"type": "String"}, "retroactiveTransaction": {"type": "Boolean"}, "sameDayTransaction": {"type": "Boolean"}, "totalStepsAwaitingAction": {"type": "Integer"}, "totalStepsCompleted": {"type": "Integer"}, "usesSubprocessInitiationDate": {"type": "Boolean"}}