{"_id": {"type": "ObjectId"}, "simplropsId": {"type": "String"}, "tenantId": {"type": "String"}, "licensedProductId": {"type": "String"}, "referenceID": {"type": "String"}, "onLeaveStatusVisibilityType": {"type": "String"}, "unitOfTime": {"type": "String"}, "enabledForWorkerType": {"type": "String"}, "leaveFamilyName": {"type": "String"}, "workdayID": {"type": "String"}, "Country": {"type": "Array", "items": {"type": "String"}}, "leaveTypeName": {"type": "String"}, "absenceTypeGroup": {"type": "String"}, "recommendations": {"type": "Array", "items": {"type": "String"}}, "versionCount": {"type": "Number"}, "hasContinuousServiceAccrualEffect": {"type": "Boolean"}, "isInactive": {"type": "Boolean"}, "hasSabbaticalEffect": {"type": "Boolean"}, "absenceAccuralEffect": {"type": "Boolean"}, "isPositionBased": {"type": "Boolean"}, "hasStockVestingEffect": {"type": "Boolean"}, "isVisibleForAbsenceTeam": {"type": "Boolean"}, "hasBenefitEffect": {"type": "Boolean"}, "hasProfessionalLeaveEffect": {"type": "Boolean"}, "hasTenureEffect": {"type": "Boolean"}, "balanceIsTracked": {"type": "Boolean"}, "hasPayrollEffect": {"type": "Boolean"}, "includeHolidays": {"type": "Number"}, "inactivateWorker": {"type": "Number"}, "isChangesFound": {"type": "Boolean"}, "isRecommendations": {"type": "String"}, "lastFunctionallyUpdated": {"type": "Date"}, "lastRetrievedDate": {"type": "Date"}, "isPII": {"type": "Boolean"}, "isArchive": {"type": "Boolean"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "__v": {"type": "Number"}}