{"_id": "ObjectId", "simplropsId": "String", "tenantId": "String", "compensationGradeName": "String", "compensationGradeReferenceID": "String", "compensationGradeWID": "String", "isChangesFound": "Boolean", "compensationGradeLastFunctionallyUpdated": "Date", "lastRetrievedDate": "Date", "allowOverride": "Boolean", "isRecommendations": "String", "Inactive": "Boolean", "compensationGradeProfiles": [{"compensationGradeProfileName": "String", "compensationGradeProfileReferenceID": "String", "compensationGradeProfileWID": "String", "compensationGradeProfileMinimum": "Number", "compensationGradeProfileMidpoint": "Number", "compensationGradeProfilesMaximum": "Number", "numberOfSegments": "Number", "compensationGradeProfilePayRangeSpread": "Number", "compensationGradeProfileCurrency": "String", "compensationGradeProfileEligibilityRuleName": "String", "compensationGradeProfilePayRangeFrequency": "String", "compensationGradeProfileDescription": "String", "compensationGradeProfileAllowsOverride": "Boolean", "compensationGradeProfileIsInactive": "Boolean", "numberOfSegments1": "Number", "compensationGradeProfileLastFunctionallyUpdatedDate": "Date", "compensationGradeProfileBasePayElements": ["String"], "_id": "ObjectId"}], "numberOfSegments": "Number", "payRangeMinimum": "Number", "payRangeMaximum": "Number", "payRangeCurrency": "String", "recommendations": ["String"], "versionCount": "Number", "isPII": "Boolean", "isArchive": "Boolean", "eligibilityRulesGroup": ["Mixed"], "createdAt": "Date", "updatedAt": "Date", "__v": "Number"}