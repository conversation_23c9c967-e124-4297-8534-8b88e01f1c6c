{"_id": {"type": "ObjectId"}, "simplropsId": {"type": "String"}, "tenantId": {"type": "String"}, "licensedProductId": {"type": "String"}, "calculationType": {"type": "String"}, "unitOfTime": {"type": "String"}, "calculationName": {"type": "String"}, "timeOffName": {"type": "String"}, "timeOffTimeEntryOption": {"type": "String"}, "timeOffType": {"type": "String"}, "timeOffRefID": {"type": "String"}, "payslipName": {"type": "String"}, "timeCalculationType": {"type": "Array"}, "validationCalculation": {"type": "Array", "items": {"type": "String"}}, "timeOffWid": {"type": "String"}, "timeOffCode": {"type": "String"}, "limitValue": {"type": "Number"}, "intermittentTimeOff": {"type": "Number"}, "absencePriority": {"type": "Number"}, "accrualRecursEvery": {"type": "Number"}, "numberOfDaysToPauseAnAccrual": {"type": "Number"}, "calculateQuantityBasedOnStartAndEndTime": {"type": "Boolean"}, "isPrivate": {"type": "Boolean"}, "displayStartAndEndTime": {"type": "Boolean"}, "isVisibleForTeamAbsence": {"type": "Boolean"}, "includeHolidaysFromHolidayCalendar": {"type": "Boolean"}, "allowPayrollInput": {"type": "Boolean"}, "isPickedUpByPayrollInterfaceOnePeriodInArrears": {"type": "Boolean"}, "calculationIsInUse": {"type": "Boolean"}, "reasonRequired": {"type": "Boolean"}, "hideFromEmployeeSelfService": {"type": "Boolean"}, "startAndEndTimeRequired": {"type": "Boolean"}, "enterThroughTimeTracking": {"type": "Boolean"}, "calculationCategory": {"type": "Array", "items": {"type": "String"}}, "eligibilityCriteria": {"type": "Array"}, "includedDays": {"type": "Array"}, "relatedCalculation": {"type": "Array"}, "scheduling": {"type": "Array"}, "schedulingCriteria": {"type": "Array"}, "timeOffReasons": {"type": "Array"}, "workerEligibility": {"type": "Array"}, "recommendations": {"type": "Array"}, "isChangesFound": {"type": "Boolean"}, "lastFunctionallyUpdated": {"type": "Date"}, "isRecommendations": {"type": "String"}, "versionCount": {"type": "Number"}, "lastRetrievedDate": {"type": "Date"}, "isPII": {"type": "Boolean"}, "isArchive": {"type": "Boolean"}, "updatedAt": {"type": "Date"}, "createdAt": {"type": "Date"}, "__v": {"type": "Number"}}