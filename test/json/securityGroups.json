{"simplropsId": {"required": true, "unique": true}, "licensedProductId": {"index": true}, "tenantId": {"required": true, "index": true}, "securityGroupName": {"index": true}, "securityGroupWid": {"required": true}, "isHrGroup": {"default": false}, "workdayID": {"index": true}, "securityGroupType": {}, "securityGroupNameText": {}, "enabledForOrgTypes": [{}], "enabledForOrgTypesCount": {}, "hideOnViewIfNotAssigned": {"default": false}, "usageCount": {"default": 0}, "roleName": {}, "restrictedToSingleAssignment": {"default": false}, "isInactive": {}, "numberOfMembers": {}, "lastFunctionallyUpdated": {}, "isWorkdayOwned": {}, "numberOfPolicies": {"default": 0}, "suspendedDomainCount": {"default": 0}, "lastRetrievedDate": {"default": "2024-07-30T06:01:01.135Z"}, "isPII": {"default": false}, "permissionsCount": {"canGet": {"es_indexed": false}, "canPut": {"es_indexed": false}, "canView": {"es_indexed": false}, "canModify": {"es_indexed": false}}, "securityGroupDomains": [{"functionalArea": [{}], "policyName": {}, "description": {}, "isActive": {"default": false}, "policyStatus": {}, "permissions": {"canGet": {}, "canPut": {}, "canView": {}, "canModify": {}}, "allowedSecurityGroupTypes": [{}]}], "businessProcessTypesAccess": [{"businessProcessType": {}, "businessProcessTypesGrantedToSecurityGroup": {"default": false}, "businessProcessTypesGrantedToSecurityGroupInitiateAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupApproveAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupCancelAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupCancelWebServiceAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupEnrichmentAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupReassignAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupRescindAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupRescindWebServiceAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupViewAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupViewCompletedAccess": {"default": false}, "businessProcessTypesGrantedToSecurityGroupCorrectAccess": {"default": false}}], "assignableRole": {}, "accessRightsToOrganizations": {}, "comment": {}, "otherUsages": [{}], "reportsAndTasks": [{}], "reportsAndTasksGetAccess": [{}], "reportsAndTasksPutAccess": [{}], "reportsAndTasksViewAccess": [{}], "reportsAndTasksModifyAccess": [{}], "intersectionOrAggregationSecurityGroup": {}, "constrainedByOrganizations": [{}], "contextType": {}, "excludedSecurityGroup": {}, "includedSecurityGroups": [{}], "membershipCount": {}, "intersectionOrAggregationAssignableRole": {}, "versionCount": {"default": 0, "index": true}}