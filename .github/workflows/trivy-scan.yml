name: <PERSON><PERSON> <PERSON><PERSON>an
run-name: <PERSON><PERSON> docker images

on:
  workflow_dispatch:

jobs:
  scan_copilot:
    name: <PERSON><PERSON> and Scan Copilot Docker image
    runs-on: arc-runner-set-dev1
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build image to Amazon ECR
        run: |
          docker buildx build --shm-size 4g -t copilot:latest . -f Dockerfile --load

      - name: Run Trivy Scan
        run: |
          trivy image \
          --db-repository public.ecr.aws/aquasecurity/trivy-db \
          --java-db-repository public.ecr.aws/aquasecurity/trivy-java-db \
          --scanners vuln \
          copilot:latest
