name: SonarQube Full Scanner

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch to compare against'
        required: true
        default: 'release/25.1'
      feature_branch:
        description: 'Feature branch to analyze'
        required: true
        default: 'master'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.base_branch }}-${{ github.event.inputs.feature_branch }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write

env:
  SONAR_HOST_URL: "https://sonarqube-npe1.simplrops.com"

# ------------------------------------------------------------
# JOB 1: SCAN THE BASE BRANCH (in parallel for all directories)
# ------------------------------------------------------------
jobs:
  base-scan:
    name: <PERSON> Scan
    runs-on: arc-runner-set-dev1

    steps:
      - name: Check out code at base branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.base_branch }}

      - name: Set up timestamp
        id: timestamp
        run: echo "TIMESTAMP=$(date +%Y%m%d%H%M%S)" >> $GITHUB_OUTPUT

      - name: Generate project name
        id: project-name
        run: |
          # Example: feature/my-new-feature_develop_20250403..._server
          RAW_PROJECT_NAME="${{ github.repository }}_${{ github.event.inputs.feature_branch }}_${{ github.event.inputs.base_branch }}_${{ steps.timestamp.outputs.TIMESTAMP }}"
          # Replace special chars with underscores
          CLEAN_PROJECT_NAME=$(echo "$RAW_PROJECT_NAME" | sed 's/[\/&^#!~@$%*+= ]/_/g')
          echo "SONARQUBE_PROJECT_NAME=$CLEAN_PROJECT_NAME" >> $GITHUB_OUTPUT

      - name: Delete/Create SonarQube Project (optional fresh start)
        run: |
          project_name="${{ steps.project-name.outputs.SONARQUBE_PROJECT_NAME }}"

          # 1) Check if project already exists
          search_response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" \
            "http://sonarqube-sonarqube.sonarqube:9000/api/projects/search?q=${project_name}")
          project_exists=$(echo "$search_response" | jq '.components | any(.key == "'$project_name'")')

          # 2) Delete it if it exists
          if [[ "$project_exists" == "true" ]]; then
            echo "Project $project_name exists; deleting..."
            delete_response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" \
              -X POST "http://sonarqube-sonarqube.sonarqube:9000/api/projects/delete?project=${project_name}")
            delete_error_response=$(echo "$delete_response" | jq 'has("errors")')

            if [[ "$delete_error_response" == "true" ]]; then
              echo "$delete_response" | jq '.errors[].msg' | grep -q "does not exist"
              if [[ $? != 0 ]]; then
                echo "Error deleting project:"
                echo "$delete_response"
                exit 1
              fi
            fi
          fi

          # 3) Create the project fresh
          response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" \
            -d "mainBranch=main&name=${project_name}&project=${project_name}" \
            "http://sonarqube-sonarqube.sonarqube:9000/api/projects/create")

          error_response=$(echo "$response" | jq 'has("errors")')
          if [[ "$error_response" == "true" ]]; then
            echo "$response" | jq '.errors[].msg' | grep -q "exists"
            if [[ $? != 0 ]]; then
              echo "Error creating project:"
              echo "$response"
              exit 1
            fi
          fi

      - name: SonarQube Scan - Base Branch
        uses: sonarsource/sonarqube-scan-action@master
        with:
          # Adjust path to the sub-folder we want to analyze
          projectBaseDir: .
          args: >
            -Dsonar.projectKey=${{ steps.project-name.outputs.SONARQUBE_PROJECT_NAME }}
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: "http://sonarqube-sonarqube.sonarqube:9000"

      - name: Save project name artifact
        run: echo "${{ steps.project-name.outputs.SONARQUBE_PROJECT_NAME }}" > project_name.txt

      - name: Upload artifact for feature-scan
        uses: actions/upload-artifact@v4
        with:
          name: project-name
          path: project_name.txt

# ------------------------------------------------------------
# JOB 2: SCAN THE FEATURE BRANCH (in parallel for all directories)
# ------------------------------------------------------------
  feature-scan:
    name: Feature Scan
    runs-on: arc-runner-set-dev1
    needs: base-scan

    steps:
      - name: Check out code at feature branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.feature_branch }}

      - name: Download artifact with project name
        uses: actions/download-artifact@v4
        with:
          name: project-name

      - name: Load project name
        id: load-project
        run: |
          PROJECT_NAME=$(cat project_name.txt)
          echo "SONARQUBE_PROJECT_NAME=$PROJECT_NAME" >> $GITHUB_OUTPUT

      - name: SonarQube Scan - Feature Branch
        uses: sonarsource/sonarqube-scan-action@master
        with:
          # Adjust path to the sub-folder
          projectBaseDir: .
          args: >
            -Dsonar.projectKey=${{ steps.load-project.outputs.SONARQUBE_PROJECT_NAME }}

        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: "http://sonarqube-sonarqube.sonarqube:9000"