name: Check Branch Alignment with Master

on:
  pull_request:

jobs:
  check-alignment:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Check if branch is up-to-date with master
        run: |
          SOURCE_BRANCH=${{ github.head_ref }}
          TARGET_BRANCH=${{ github.event.pull_request.base.ref }}

          echo "Source Branch: $SOURCE_BRANCH"
          echo "Target Branch: $TARGET_BRANCH"

          ALIGNED=$(git rev-list --left-only --count origin/$TARGET_BRANCH...origin/$SOURCE_BRANCH)
          echo $ALIGNED
          if [ "$ALIGNED" != "0" ]; then
            echo "Branches are not aligned. Please check if the PR is aligned with the branch master"
            exit 1
          else
            echo "Branches are aligned with master"
          fi
