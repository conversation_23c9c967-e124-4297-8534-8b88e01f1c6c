name: Build and Deploy
run-name: Build & Deploy copilot on ${{ inputs.environment }} environments

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Environment
        default: dev1
        options:
          - dev1
          - test1
          - dev2
          - test2
      deploy:
        type: boolean
        description: Deploy on central portal?
        default: false

env:
  NPE_K8S_DEPLOYMENT_BRANCH: development
  K8S_DEPLOYMENT_BRANCH: master

jobs:
  build_copilot:
    name: Build and Publish Copilot Docker image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.run_number }}
        run: |
          ECR_REPOSITORY="${{ inputs.environment }}/copilot"

          echo "ECR_REPOSITORY=$ECR_REPOSITORY"
          echo "ECR_REPOSITORY=$ECR_REPOSITORY" >> $GITHUB_ENV

          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG . -f Dockerfile
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  tenant-upgrade:
    name: Tenant Upgrade - ${{ inputs.environment }}
    if: ${{ inputs.deploy }}
    needs: [build_copilot]
    runs-on: arc-runner-set-${{ inputs.environment }}
    outputs:
      COPILOT_SERVICE_OLD_TAG: ${{ steps.get-tenant-values.outputs.COPILOT_SERVICE_OLD_TAG }}
    steps:
      - name: Checkout k8s-deployment Repository
        if: ${{ inputs.environment == 'dev1' || inputs.environment == 'test1' || inputs.environment == 'dev2' || inputs.environment == 'test2' }}
        uses: actions/checkout@v4
        with:
          repository: simplrops/k8s-deployment
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          ref: ${{ env.NPE_K8S_DEPLOYMENT_BRANCH }}

      - name: Checkout k8s-deployment Repository
        if: ${{ inputs.environment != 'dev1' && inputs.environment != 'test1' && inputs.environment != 'dev2' && inputs.environment != 'test2' }}
        uses: actions/checkout@v4
        with:
          repository: simplrops/k8s-deployment
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          ref: ${{ env.K8S_DEPLOYMENT_BRANCH }}

      - name: Setup kube-config
        run: |
          if [[ "${{ inputs.environment }}" == "dev1" ]]
          then
            export CLUSTER_NAME="nonprod1-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "test1" ]]
          then
            export CLUSTER_NAME="nonprod1-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "dev2" ]]
          then
            export CLUSTER_NAME="nonprod2-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "test2" ]]
          then
            export CLUSTER_NAME="nonprod2-ekscluster"
            export AWS_REGION=ap-south-1
          fi

          export KUBECONFIG="~/.kube/config"
          echo "Cluster Name: $CLUSTER_NAME"
          aws eks update-kubeconfig --name ${CLUSTER_NAME} --region ${AWS_REGION}

      - name: Get Tenant Values
        id: get-tenant-values
        run: |
          check_helm_status() {   helm status ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | grep -q "STATUS: deployed";   return $?; }
          SECONDS_ELAPSED=0
          CHECK_INTERVAL=10
          TIMEOUT=300
          while ! check_helm_status; do
            if [ $SECONDS_ELAPSED -ge $TIMEOUT ]; then
              echo "Timeout expired. Performing rollback..."
              helm rollback ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central
              if [ $? -eq 0 ]; then
                echo "Helm rollback successful."
              else
                echo "Helm rollback failed."
                exit 1
              fi
              break
            fi

            echo "Waiting for the current Helm release to complete..."
            sleep $CHECK_INTERVAL
            SECONDS_ELAPSED=$((SECONDS_ELAPSED + CHECK_INTERVAL))
          done

          helm get values ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | sed '1d' > tenant.yaml

          echo "COPILOT_SERVICE_OLD_TAG=$(yq -r '.copilot.image.tag' tenant.yaml)" >> $GITHUB_OUTPUT
          echo "COPILOT_SERVICE_OLD_TAG=$(yq -r '.copilot.image.tag' tenant.yaml)"

      - name: Upgrade Copilot service
        run: |
          check_helm_status() {   helm status ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | grep -q "STATUS: deployed";   return $?; }
          SECONDS_ELAPSED=0
          CHECK_INTERVAL=10
          TIMEOUT=300
          while ! check_helm_status; do
            if [ $SECONDS_ELAPSED -ge $TIMEOUT ]; then
              echo "Timeout expired. Performing rollback..."
              helm rollback ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central
              if [ $? -eq 0 ]; then
                echo "Helm rollback successful."
              else
                echo "Helm rollback failed."
                exit 1
              fi
              break
            fi

            echo "Waiting for the current Helm release to complete..."
            sleep $CHECK_INTERVAL
            SECONDS_ELAPSED=$((SECONDS_ELAPSED + CHECK_INTERVAL))
          done

          helm upgrade --install --wait ${{ inputs.environment }}-central \
            --namespace ${{ inputs.environment }}-central \
            --set copilot.image.tag="${{ github.run_number }}" \
            helm-charts/central -f tenant.yaml

          rm tenant.yaml

  send-ms-team-notification:
    name: Notify Team - ${{ inputs.environment }}
    needs: tenant-upgrade
    if: ${{ inputs.deploy }}
    runs-on: arc-runner-set-${{ inputs.environment }}
    steps:
      - name: Send notification to MS Teams for Copilot service
        env:
          WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_URL }}
          COPILOT_SERVICE_OLD_TAG: ${{ needs.tenant-upgrade.outputs.COPILOT_SERVICE_OLD_TAG }}
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            MESSAGE="✅ The **Central Tenant** has been successfully upgraded. 🎉"
          else
            MESSAGE="❌ The **Central Tenant** upgraded has been failed. 😞"
          fi
          curl -X POST $WEBHOOK_URL \
            -H "Content-Type: application/json" \
            -d "{
              \"attachments\": [{\"contentType\":\"application/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\": {\"$schema\":\"http://adaptivecards.io/schemas/adaptive-card.json\",\"type\":\"AdaptiveCard\",\"version\":\"1.2\",\"body\": [{\"type\": \"TextBlock\", \"text\": \"${MESSAGE}\n\nHere are the details:\n\n**Author:** ${{ github.actor }}\n\n**Environment:** ${{ inputs.environment }}\n\n**Branch Name:** ${{ github.ref_name }}\n\n**Copilot Service:** $COPILOT_SERVICE_OLD_TAG -> ${{ github.run_number }}\"}]}}]}"
