name: Replease docker
run-name: Replease docker on ${{ inputs.base_environment }}/copilot:${{ inputs.base_version }} to ${{ inputs.release_version }}

on:
  workflow_dispatch:
    inputs:
      base_environment:
        type: choice
        description: Base environment
        default: test1
        options:
          - dev1
          - test1
          - dev2
          - test2
      base_version:
        type: string
        description: Base version
        required: true
      release_environment:
        type: choice
        description: Release environment
        default: production
        options:
          - production
          - preprod
      release_version:
        type: string
        description: Release version
        required: true

env:
  SERVICE_NAME: copilot
  BASE_ECR_ARN: 432965710617.dkr.ecr.ap-south-1.amazonaws.com
  RELEASE_ECR_ARN: 905418425158.dkr.ecr.us-west-1.amazonaws.com

jobs:
  release_docker:
    name: Replease docker
    runs-on: arc-runner-set-dev1
    steps:
      - name: Login to Base Amazon ECR
        run: |
          aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin $BASE_ECR_ARN

      - name: Pull image frpm Base Amazon ECR
        run: |
          BASE_ECR_REPOSITORY="$BASE_ECR_ARN/${{ inputs.base_environment }}/$SERVICE_NAME"
          echo "BASE_ECR_REPOSITORY=$BASE_ECR_REPOSITORY"
          docker pull $BASE_ECR_REPOSITORY:${{ inputs.base_version }}

      - name: Login to Release Amazon ECR
        run: |
          aws ecr get-login-password --region us-west-1 | docker login --username AWS --password-stdin $RELEASE_ECR_ARN

      - name: Push image to Release Amazon ECR
        run: |
          BASE_ECR_REPOSITORY="$BASE_ECR_ARN/${{ inputs.base_environment }}/$SERVICE_NAME"
          RELEASE_ECR_REPOSITORY="$RELEASE_ECR_ARN/${{ inputs.release_environment }}/$SERVICE_NAME"

          echo "BASE_ECR_REPOSITORY=$BASE_ECR_REPOSITORY"
          echo "RELEASE_ECR_REPOSITORY=$RELEASE_ECR_REPOSITORY"

          docker tag $BASE_ECR_REPOSITORY:${{ inputs.base_version }} $RELEASE_ECR_REPOSITORY:${{ inputs.release_version }}
          docker push $RELEASE_ECR_REPOSITORY:${{ inputs.release_version }}
