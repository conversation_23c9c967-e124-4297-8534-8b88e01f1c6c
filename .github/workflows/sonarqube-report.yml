name: SonarQube code scanner

on:
  pull_request

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write

env:
  REPOSITORY_NAME: ${{ github.event.repository.name }}
  SONAR_HOST_URL: "https://sonarqube-npe1.simplrops.com"

jobs:
  code-scanner:
    name: sonarqube code scanner
    runs-on: arc-runner-set-dev1
    steps:
      - name: Check out the codebase.
        uses: actions/checkout@v2

      - name: Generate project name
        id: sonarqube-project-name
        run: |
          # replace special characters with underscore(_) and generate project name
          echo SONARQUBE_PROJECT_NAME=$(echo ${REPOSITORY_NAME}:app:${GITHUB_HEAD_REF} | sed 's/[\/&^#!~@$%*+= ]/_/g') >> $GITHUB_OUTPUT

      - name: Create project with branch name
        run: |
          # get project name
          project_name=${{ steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME }}
        
          # Search for the project in SonarQube
          search_response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" "http://sonarqube-sonarqube.sonarqube:9000/api/projects/search?q=${project_name}")
          project_exists=$(echo $search_response | jq '.components | any(.key == "'$project_name'")')

          # Delete the project if it already exists
          if [[ $project_exists == true ]]
          then
            echo "Project exists, deleting..."
            delete_response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" -X POST "http://sonarqube-sonarqube.sonarqube:9000/api/projects/delete?project=${project_name}")
            delete_error_response=$(echo $delete_response | jq 'has("errors")')
            
            # Check if delete response has any errors
            if [[ $delete_error_response == true ]]
            then
              # Check if error response mentions that project does not exist
              echo $delete_response | jq '.errors[].msg' | grep "does not exist" > /dev/null
              if [[ $? == 1 ]]
              then
                echo $delete_response
                exit 1
              fi
            fi
          else
            echo "Project does not exist, no need to delete."
          fi

          # Create project with branch name
          response=$(curl --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" -d "mainBranch:main&name=${project_name}&project=${project_name}" "http://sonarqube-sonarqube.sonarqube:9000/api/projects/create")
          error_response=$(echo $response | jq 'has("errors")')
          
          # check if response has any errors
          if [[ $error_response == true ]]
          then
              # check if error response mentions about project is existing
              echo $response | jq '.errors[].msg' | grep "exists" > /dev/null
              if [[ $? == 1 ]]
              then
                  echo $response
                  exit 1
              fi
          fi

      - name: Show Changed Files
        run: |
          git fetch origin master:master
          DIFF_FILES=$(git diff --name-only --diff-filter=d master | tr '\n' ',' | sed 's/\(.*\),/\1 /')
          echo $DIFF_FILES
          echo "DIFF_FILES=${DIFF_FILES}" >> $GITHUB_ENV

          DIFF_FILES_MASTER=$(git diff --name-only --diff-filter=adr master | tr '\n' ',' | sed 's/\(.*\),/\1 /')
          echo $DIFF_FILES_MASTER
          echo "DIFF_FILES_MASTER=${DIFF_FILES_MASTER}" >> $GITHUB_ENV

      - name: Fetch and Checkout Master Branch
        run: |
          git fetch origin master:master
          git checkout master

      - name: Sonarqube Scan Without Changes
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: ./
          args: >
            -Dsonar.projectKey=${{ steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME }}
            -Dsonar.sources=${{ env.DIFF_FILES_MASTER }}
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: "http://sonarqube-sonarqube.sonarqube:9000"

      - name: Checkout PR Branch
        if: github.ref != 'refs/heads/master'
        uses: actions/checkout@v2
        with:
          ref: ${{ github.head_ref }}
          clean: false

      - name: Sonarqube Scan
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: ./
          args: >
            -Dsonar.projectKey=${{ steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME }}
            -Dsonar.sources=${{ env.DIFF_FILES }}
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: "http://sonarqube-sonarqube.sonarqube:9000"

      - name: Get sonarqube report results
        id: sonarqube-report
        run: |
          # get project name
          project_name=${{ steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME }}
          sleep 20
          curl -s -o sonarqube-report.json --header "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" "http://sonarqube-sonarqube.sonarqube:9000/api/measures/component?additionalFields=metrics&component=${project_name}&metricKeys=quality_gate_details,new_bugs,new_code_smells,new_vulnerabilities,new_security_hotspots"
          echo "NEW_BUGS=$(cat sonarqube-report.json | jq -r '.component.measures[] | select(.metric == "new_bugs") | .period.value')" >> $GITHUB_OUTPUT
          echo "NEW_CODE_SMELLS=$(cat sonarqube-report.json | jq -r '.component.measures[] | select(.metric == "new_code_smells") | .period.value')" >> $GITHUB_OUTPUT
          echo "NEW_VULNERABILITIES=$(cat sonarqube-report.json | jq -r '.component.measures[] | select(.metric == "new_vulnerabilities") | .period.value')" >> $GITHUB_OUTPUT
          echo "NEW_SECURITY_HOTSPOTS=$(cat sonarqube-report.json | jq -r '.component.measures[] | select(.metric == "new_security_hotspots") | .period.value')" >> $GITHUB_OUTPUT
          echo "QUALITY_GATE_STATUS=$(cat sonarqube-report.json | jq -r '.component.measures[] | select(.metric == "quality_gate_details") | .value' | jq -r '.level')" >> $GITHUB_OUTPUT

          cat sonarqube-report.json

      - name: Comment sonarqube results on the PR
        if: ${{ steps.sonarqube-report.outputs.NEW_BUGS == '0' && steps.sonarqube-report.outputs.NEW_CODE_SMELLS == '0' && steps.sonarqube-report.outputs.NEW_SECURITY_HOTSPOTS == '0' && steps.sonarqube-report.outputs.NEW_VULNERABILITIES == '0' && steps.sonarqube-report.outputs.QUALITY_GATE_STATUS == 'OK' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const sonarqubeHostUrl = process.env.SONAR_HOST_URL;
            const sonarqubeProjectName = `${{steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME}}`;
            const bugs = `${{steps.sonarqube-report.outputs.NEW_BUGS}}`;
            const codeSmells = `${{steps.sonarqube-report.outputs.NEW_CODE_SMELLS}}`;
            const vulnerabilities = `${{steps.sonarqube-report.outputs.NEW_VULNERABILITIES}}`;
            const securityHotspots = `${{steps.sonarqube-report.outputs.NEW_SECURITY_HOTSPOTS}}`;
            const qualityGateStatus = `${{steps.sonarqube-report.outputs.QUALITY_GATE_STATUS}}`;
            
            const issueComment = `
            🔍 **SonarQube Quality Gate Review:**
            🎉 **Great job!** Your code has no bugs and code smells, and it passed the quality gate. Keep up the good work and maintain the high quality of your code! 🌟"
            For detailed insights, visit the Detailed Scan Insights: - [Project Dashboard](${sonarqubeHostUrl}/dashboard?id=${sonarqubeProjectName}&codeScope=new)
            `;

            const comment = await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: issueComment,
            });
            console.log(`Comment created: ${comment.data.html_url}`);

      - name: Comment sonarqube results on the PR
        if: ${{ steps.sonarqube-report.outputs.NEW_BUGS != '0' || steps.sonarqube-report.outputs.NEW_CODE_SMELLS != '0' || steps.sonarqube-report.outputs.NEW_SECURITY_HOTSPOTS != '0' || steps.sonarqube-report.outputs.NEW_VULNERABILITIES != '0' || steps.sonarqube-report.outputs.QUALITY_GATE_STATUS != 'OK' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const sonarqubeHostUrl = process.env.SONAR_HOST_URL;
            const sonarqubeProjectName = `${{steps.sonarqube-project-name.outputs.SONARQUBE_PROJECT_NAME}}`;
            const bugs = `${{steps.sonarqube-report.outputs.NEW_BUGS}}`;
            const codeSmells = `${{steps.sonarqube-report.outputs.NEW_CODE_SMELLS}}`;
            const vulnerabilities = `${{steps.sonarqube-report.outputs.NEW_VULNERABILITIES}}`;
            const securityHotspots = `${{steps.sonarqube-report.outputs.NEW_SECURITY_HOTSPOTS}}`;
            const qualityGateStatus = `${{steps.sonarqube-report.outputs.QUALITY_GATE_STATUS}}`;
            
            const issueComment = `
            🔍 **[Backend] SonarQube Quality Gate Review:**
            - **Bugs:** ${bugs} 🐞
            - **Code Smells:** ${codeSmells} 🌿
            - **Vulnerabilities:** ${vulnerabilities} 🛡️
            - **Security Hotspots:** ${securityHotspots} 🔥
            - **Quality Gate Status:** ${qualityGateStatus} 🚦

            For detailed insights, visit the Detailed Scan Insights: [Project Dashboard](${sonarqubeHostUrl}/dashboard?id=${sonarqubeProjectName}&codeScope=new)
            `;

            const comment = await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: issueComment,
            });
            console.log(`Comment created: ${comment.data.html_url}`);
