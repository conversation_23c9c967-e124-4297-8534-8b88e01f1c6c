
name: <PERSON>ter

on:
  pull_request:

jobs:
  black:
    name: lint/black
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: psf/black@stable
        with:
          options: "--check --verbose"
          version: "23.11.0"

  isort:
    name: lint/isort
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-python@v5
        with:
          python-version: 3.11

      - uses: isort/isort-action@v1
        with:
          isortVersion: 5.13.2
          configuration: "--check-only --verbose --profile black --filter-files --line-length=88"
          # Note: On the official website of isort,
          # they provide the TIP that it is good to add requirements.txt
          # in github action workflow.
          # https://pycqa.github.io/isort/docs/configuration/github_action.html#isort-result
          requirements-files: "requirements.txt"
