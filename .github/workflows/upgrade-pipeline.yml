name: Upgrade Pipeline
run-name: Upgrade Pipeline ${{ inputs.environment }} - ${{ inputs.version }}

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Environment
        default: dev1
        options:
          - dev1
          - test1
          - dev2
          - test2
          - demo
          - preprod
          - internal
          - dt-aus
          - dt-can
          - dt-belgium
          - ibm
          - emd
          - ocg
      version:
        type: string
        description: Version
        default: latest

env:
  NPE_K8S_DEPLOYMENT_BRANCH: development
  K8S_DEPLOYMENT_BRANCH: master

jobs:
  tenant-upgrade:
    name: Tenant Upgrade - ${{ inputs.environment }}
    runs-on: arc-runner-set-${{ inputs.environment }}
    outputs:
      COPILOT_SERVICE_OLD_TAG: ${{ steps.get-tenant-values.outputs.COPILOT_SERVICE_OLD_TAG }}
    steps:
      - name: Production change user check
        if: ${{ inputs.environment != 'dev1' && inputs.environment != 'test1' && inputs.environment != 'dev2' && inputs.environment != 'test2' }}
        run: |
          allowed_users=("so-nitaliya" "harshitSimplrOps" "mihir9049" "suryavijaysinh15")
          GITHUB_ACTOR="${{ github.actor }}"

          # Check if github.actor is in the list of allowed users
          if [[ ! " ${allowed_users[@]} " =~ " ${GITHUB_ACTOR} " ]]; then
            echo "Actor ${GITHUB_ACTOR} is not allowed to perform this action."
            exit 1
          else
            echo "Actor ${GITHUB_ACTOR} is allowed to perform this action."
          fi

      - name: Checkout k8s-deployment Repository
        if: ${{ inputs.environment == 'dev1' || inputs.environment == 'test1' || inputs.environment == 'dev2' || inputs.environment == 'test2' }}
        uses: actions/checkout@v4
        with:
          repository: simplrops/k8s-deployment
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          ref: ${{ env.NPE_K8S_DEPLOYMENT_BRANCH }}

      - name: Checkout k8s-deployment Repository
        if: ${{ inputs.environment != 'dev1' && inputs.environment != 'test1' && inputs.environment != 'dev2' && inputs.environment != 'test2' }}
        uses: actions/checkout@v4
        with:
          repository: simplrops/k8s-deployment
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          ref: ${{ env.K8S_DEPLOYMENT_BRANCH }}

      - name: Setup kube-config
        run: |
          if [[ "${{ inputs.environment }}" == "dev1" ]]
          then
            export CLUSTER_NAME="nonprod1-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "test1" ]]
          then
            export CLUSTER_NAME="nonprod1-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "dev2" ]]
          then
            export CLUSTER_NAME="nonprod2-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "test2" ]]
          then
            export CLUSTER_NAME="nonprod2-ekscluster"
            export AWS_REGION=ap-south-1
          elif [[ "${{ inputs.environment }}" == "ibm" ]]
          then
            export CLUSTER_NAME="ibm-ekscluster"
            export AWS_REGION=us-west-2
          elif [[ "${{ inputs.environment }}" == "dt-aus" ]]
          then
            export CLUSTER_NAME="deloitteaus-ekscluster"
            export AWS_REGION=ap-southeast-2
          elif [[ "${{ inputs.environment }}" == "dt-belgium" ]]
          then
            export CLUSTER_NAME="deloittebelgium-ekscluster"
            export AWS_REGION=eu-central-1
          elif [[ "${{ inputs.environment }}" == "dt-can" ]]
          then
            export CLUSTER_NAME="deloittecanada-ekscluster"
            export AWS_REGION=ca-central-1
          elif [[ "${{ inputs.environment }}" == "emd" ]]
          then
            export CLUSTER_NAME="emd-ekscluster"
            export AWS_REGION=us-west-2
          elif [[ "${{ inputs.environment }}" == "internal" ]]
          then
            export CLUSTER_NAME="production-ekscluster"
            export AWS_REGION=us-west-1
          elif [[ "${{ inputs.environment }}" == "demo" ]]
          then
            export CLUSTER_NAME="production-ekscluster"
            export AWS_REGION=us-west-1
          elif [[ "${{ inputs.environment }}" == "ocg" ]]
          then
            export CLUSTER_NAME="ocg-ekscluster"
            export AWS_REGION=us-east-1
          elif [[ "${{ inputs.environment }}" == "preprod" ]]
          then
            export CLUSTER_NAME="preprod-ekscluster"
            export AWS_REGION=ap-south-1
          fi

          export KUBECONFIG="~/.kube/config"
          echo "Cluster Name: $CLUSTER_NAME"
          aws eks update-kubeconfig --name ${CLUSTER_NAME} --region ${AWS_REGION}

      - name: Get Tenant Values
        id: get-tenant-values
        run: |
          check_helm_status() {   helm status ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | grep -q "STATUS: deployed";   return $?; }
          SECONDS_ELAPSED=0
          CHECK_INTERVAL=10
          TIMEOUT=300
          while ! check_helm_status; do
            if [ $SECONDS_ELAPSED -ge $TIMEOUT ]; then
              echo "Timeout expired. Performing rollback..."
              helm rollback ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central
              if [ $? -eq 0 ]; then
                echo "Helm rollback successful."
              else
                echo "Helm rollback failed."
                exit 1
              fi
              break
            fi

            echo "Waiting for the current Helm release to complete..."
            sleep $CHECK_INTERVAL
            SECONDS_ELAPSED=$((SECONDS_ELAPSED + CHECK_INTERVAL))
          done

          helm get values ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | sed '1d' > tenant.yaml

          echo "COPILOT_SERVICE_OLD_TAG=$(yq -r '.copilot.image.tag' tenant.yaml)" >> $GITHUB_OUTPUT
          echo "COPILOT_SERVICE_OLD_TAG=$(yq -r '.copilot.image.tag' tenant.yaml)"

      - name: Upgrade Copilot service
        run: |
          check_helm_status() {   helm status ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central | grep -q "STATUS: deployed";   return $?; }
          SECONDS_ELAPSED=0
          CHECK_INTERVAL=10
          TIMEOUT=300
          while ! check_helm_status; do
            if [ $SECONDS_ELAPSED -ge $TIMEOUT ]; then
              echo "Timeout expired. Performing rollback..."
              helm rollback ${{ inputs.environment }}-central -n ${{ inputs.environment }}-central
              if [ $? -eq 0 ]; then
                echo "Helm rollback successful."
              else
                echo "Helm rollback failed."
                exit 1
              fi
              break
            fi

            echo "Waiting for the current Helm release to complete..."
            sleep $CHECK_INTERVAL
            SECONDS_ELAPSED=$((SECONDS_ELAPSED + CHECK_INTERVAL))
          done

          helm upgrade --install --wait ${{ inputs.environment }}-central \
            --namespace ${{ inputs.environment }}-central \
            --set copilot.image.tag="${{ inputs.version }}" \
            helm-charts/central -f tenant.yaml

          rm tenant.yaml

  send-ms-team-notification:
    name: Notify Team - ${{ inputs.environment }}
    needs: tenant-upgrade
    runs-on: arc-runner-set-${{ inputs.environment }}
    steps:
      - name: Send notification to MS Teams for Copilot service
        env:
          WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_URL }}
          COPILOT_SERVICE_OLD_TAG: ${{ needs.tenant-upgrade.outputs.COPILOT_SERVICE_OLD_TAG }}
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            MESSAGE="✅ The **Central Tenant** has been successfully upgraded. 🎉"
          else
            MESSAGE="❌ The **Central Tenant** upgraded has been failed. 😞"
          fi
          curl -X POST $WEBHOOK_URL \
            -H "Content-Type: application/json" \
            -d "{
              \"attachments\": [{\"contentType\":\"application/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\": {\"$schema\":\"http://adaptivecards.io/schemas/adaptive-card.json\",\"type\":\"AdaptiveCard\",\"version\":\"1.2\",\"body\": [{\"type\": \"TextBlock\", \"text\": \"${MESSAGE}\n\nHere are the details:\n\n**Author:** ${{ github.actor }}\n\n**Environment:** ${{ inputs.environment }}\n\n**Branch Name:** ${{ github.ref_name }}\n\n**Copilot Service:** $COPILOT_SERVICE_OLD_TAG -> ${{ github.run_number }}\"}]}}]}"
