"""
Tests for Pydantic models and schemas.
"""
import pytest
from pydantic import ValidationError

from app.models.schemas import (
    ClassifyRequest,
    GenerateQueryRequest,
    ClassifyResponse,
    GenerateQueryResponse,
    ErrorResponse
)


@pytest.mark.unit
class TestClassifyRequest:
    """Test cases for ClassifyRequest model."""

    def test_classify_request_valid(self):
        """Test valid ClassifyRequest creation."""
        # Test data
        data = {"prompt": "What is SimplrOps?"}
        
        # Create model instance
        request = ClassifyRequest(**data)
        
        # Assertions
        assert request.prompt == "What is SimplrOps?"

    def test_classify_request_empty_prompt(self):
        """Test ClassifyRequest with empty prompt."""
        # Test data
        data = {"prompt": ""}
        
        # Create model instance
        request = ClassifyRequest(**data)
        
        # Assertions
        assert request.prompt == ""

    def test_classify_request_missing_prompt(self):
        """Test ClassifyRequest with missing prompt field."""
        # Test data without required field
        data = {}
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            ClassifyRequest(**data)
        
        assert "prompt" in str(exc_info.value)

    def test_classify_request_extra_fields(self):
        """Test ClassifyRequest with extra fields."""
        # Test data with extra field
        data = {
            "prompt": "What is SimplrOps?",
            "extra_field": "should be ignored"
        }
        
        # Create model instance
        request = ClassifyRequest(**data)
        
        # Assertions - extra field should be ignored
        assert request.prompt == "What is SimplrOps?"
        assert not hasattr(request, 'extra_field')

    def test_classify_request_long_prompt(self):
        """Test ClassifyRequest with very long prompt."""
        # Test data with long prompt
        long_prompt = "What is SimplrOps? " * 1000
        data = {"prompt": long_prompt}
        
        # Create model instance
        request = ClassifyRequest(**data)
        
        # Assertions
        assert request.prompt == long_prompt

    def test_classify_request_special_characters(self):
        """Test ClassifyRequest with special characters in prompt."""
        # Test data with special characters
        special_prompt = "What is SimplrOps? 🤖 How does it work with émojis and spëcial chars?"
        data = {"prompt": special_prompt}
        
        # Create model instance
        request = ClassifyRequest(**data)
        
        # Assertions
        assert request.prompt == special_prompt


@pytest.mark.unit
class TestGenerateQueryRequest:
    """Test cases for GenerateQueryRequest model."""

    def test_generate_query_request_valid(self):
        """Test valid GenerateQueryRequest creation."""
        # Test data
        data = {
            "prompt": "Show me all security groups",
            "query_type": "list",
            "chart_type": "table",
            "dbSchema": {
                "collections": {
                    "securityGroups": {
                        "fields": ["simplropsId", "securityGroupName"]
                    }
                }
            }
        }
        
        # Create model instance
        request = GenerateQueryRequest(**data)
        
        # Assertions
        assert request.prompt == "Show me all security groups"
        assert request.query_type == "list"
        assert request.chart_type == "table"
        assert request.dbSchema == data["dbSchema"]

    def test_generate_query_request_optional_chart_type(self):
        """Test GenerateQueryRequest with optional chart_type as None."""
        # Test data without chart_type
        data = {
            "prompt": "Count security groups",
            "query_type": "count",
            "chart_type": None,
            "dbSchema": {"collections": {"securityGroups": {"fields": ["simplropsId"]}}}
        }
        
        # Create model instance
        request = GenerateQueryRequest(**data)
        
        # Assertions
        assert request.chart_type is None

    def test_generate_query_request_missing_chart_type(self):
        """Test GenerateQueryRequest without chart_type field."""
        # Test data without chart_type field
        data = {
            "prompt": "Count security groups",
            "query_type": "count",
            "dbSchema": {"collections": {"securityGroups": {"fields": ["simplropsId"]}}}
        }
        
        # Create model instance
        request = GenerateQueryRequest(**data)
        
        # Assertions - should default to None
        assert request.chart_type is None

    def test_generate_query_request_missing_required_fields(self):
        """Test GenerateQueryRequest with missing required fields."""
        # Test data missing required fields
        data = {"prompt": "Show me data"}
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            GenerateQueryRequest(**data)
        
        error_str = str(exc_info.value)
        assert "query_type" in error_str
        assert "dbSchema" in error_str

    def test_generate_query_request_complex_schema(self):
        """Test GenerateQueryRequest with complex database schema."""
        # Test data with complex schema
        data = {
            "prompt": "Show me business processes with security groups",
            "query_type": "list",
            "chart_type": "bar",
            "dbSchema": {
                "collections": {
                    "businessProcessEvents": {
                        "fields": ["simplropsId", "businessProcessName", "securityGroups"],
                        "relationships": {
                            "securityGroups": {
                                "type": "array",
                                "ref": "securityGroups"
                            }
                        }
                    },
                    "securityGroups": {
                        "fields": ["simplropsId", "securityGroupName", "isInactive"]
                    }
                }
            }
        }
        
        # Create model instance
        request = GenerateQueryRequest(**data)
        
        # Assertions
        assert request.dbSchema == data["dbSchema"]
        assert "businessProcessEvents" in request.dbSchema["collections"]
        assert "securityGroups" in request.dbSchema["collections"]

    def test_generate_query_request_invalid_schema_type(self):
        """Test GenerateQueryRequest with invalid schema type."""
        # Test data with invalid schema type
        data = {
            "prompt": "Show me data",
            "query_type": "list",
            "dbSchema": "invalid_schema_type"  # Should be dict
        }
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            GenerateQueryRequest(**data)
        
        assert "dbSchema" in str(exc_info.value)


@pytest.mark.unit
class TestClassifyResponse:
    """Test cases for ClassifyResponse model."""

    def test_classify_response_valid(self):
        """Test valid ClassifyResponse creation."""
        # Test data
        data = {
            "success": True,
            "data": "SimplrOps is a comprehensive platform for managing Workday configurations."
        }
        
        # Create model instance
        response = ClassifyResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == data["data"]

    def test_classify_response_dict_data(self):
        """Test ClassifyResponse with dictionary data."""
        # Test data with dict
        data = {
            "success": True,
            "data": {
                "dbCollection": "securityGroups",
                "responseType": "list",
                "chart_type": "table"
            }
        }
        
        # Create model instance
        response = ClassifyResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == data["data"]

    def test_classify_response_list_data(self):
        """Test ClassifyResponse with list data."""
        # Test data with list
        data = {
            "success": True,
            "data": ["item1", "item2", "item3"]
        }
        
        # Create model instance
        response = ClassifyResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == data["data"]

    def test_classify_response_failure(self):
        """Test ClassifyResponse for failure case."""
        # Test data for failure
        data = {
            "success": False,
            "data": "Classification failed due to invalid input"
        }
        
        # Create model instance
        response = ClassifyResponse(**data)
        
        # Assertions
        assert response.success is False
        assert response.data == data["data"]

    def test_classify_response_missing_fields(self):
        """Test ClassifyResponse with missing required fields."""
        # Test data missing required fields
        data = {"success": True}
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            ClassifyResponse(**data)
        
        assert "data" in str(exc_info.value)


@pytest.mark.unit
class TestGenerateQueryResponse:
    """Test cases for GenerateQueryResponse model."""

    def test_generate_query_response_valid(self):
        """Test valid GenerateQueryResponse creation."""
        # Test data
        data = {
            "success": True,
            "data": '[{"$match": {"isInactive": {"$ne": true}}}, {"$project": {"_id": 0, "simplropsId": 1}}]'
        }
        
        # Create model instance
        response = GenerateQueryResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == data["data"]

    def test_generate_query_response_complex_query(self):
        """Test GenerateQueryResponse with complex MongoDB query."""
        # Test data with complex query
        complex_query = '''[
            {"$match": {"isInactive": {"$ne": true}, "securityGroupName": {"$regex": "Admin", "$options": "i"}}},
            {"$lookup": {"from": "policies", "localField": "simplropsId", "foreignField": "securityGroupId", "as": "policies"}},
            {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1, "policyCount": {"$size": "$policies"}}}
        ]'''
        
        data = {
            "success": True,
            "data": complex_query
        }
        
        # Create model instance
        response = GenerateQueryResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == complex_query

    def test_generate_query_response_failure(self):
        """Test GenerateQueryResponse for failure case."""
        # Test data for failure
        data = {
            "success": False,
            "data": "Query generation failed"
        }
        
        # Create model instance
        response = GenerateQueryResponse(**data)
        
        # Assertions
        assert response.success is False
        assert response.data == data["data"]

    def test_generate_query_response_empty_query(self):
        """Test GenerateQueryResponse with empty query."""
        # Test data with empty query
        data = {
            "success": True,
            "data": ""
        }
        
        # Create model instance
        response = GenerateQueryResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.data == ""

    def test_generate_query_response_missing_fields(self):
        """Test GenerateQueryResponse with missing required fields."""
        # Test data missing required fields
        data = {"success": True}
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            GenerateQueryResponse(**data)
        
        assert "data" in str(exc_info.value)


@pytest.mark.unit
class TestErrorResponse:
    """Test cases for ErrorResponse model."""

    def test_error_response_valid(self):
        """Test valid ErrorResponse creation."""
        # Test data
        data = {
            "success": False,
            "detail": "Invalid query format"
        }
        
        # Create model instance
        response = ErrorResponse(**data)
        
        # Assertions
        assert response.success is False
        assert response.detail == "Invalid query format"

    def test_error_response_default_success(self):
        """Test ErrorResponse with default success value."""
        # Test data without success field
        data = {
            "detail": "Authentication failed"
        }
        
        # Create model instance
        response = ErrorResponse(**data)
        
        # Assertions - success should default to False
        assert response.success is False
        assert response.detail == "Authentication failed"

    def test_error_response_override_success(self):
        """Test ErrorResponse with overridden success value."""
        # Test data with success=True (unusual but allowed)
        data = {
            "success": True,
            "detail": "Warning message"
        }
        
        # Create model instance
        response = ErrorResponse(**data)
        
        # Assertions
        assert response.success is True
        assert response.detail == "Warning message"

    def test_error_response_long_detail(self):
        """Test ErrorResponse with long detail message."""
        # Test data with long detail
        long_detail = "This is a very long error message that describes in detail what went wrong during the processing of the request. " * 10
        data = {
            "detail": long_detail
        }
        
        # Create model instance
        response = ErrorResponse(**data)
        
        # Assertions
        assert response.detail == long_detail

    def test_error_response_missing_detail(self):
        """Test ErrorResponse with missing detail field."""
        # Test data missing required field
        data = {"success": False}
        
        # Should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            ErrorResponse(**data)
        
        assert "detail" in str(exc_info.value)


@pytest.mark.unit
class TestSchemaIntegration:
    """Integration tests for schema models."""

    def test_all_schemas_json_serializable(self):
        """Test that all schema models are JSON serializable."""
        # Create instances of all models
        classify_request = ClassifyRequest(prompt="Test prompt")
        
        generate_query_request = GenerateQueryRequest(
            prompt="Test query",
            query_type="list",
            dbSchema={"collections": {"test": {"fields": ["id"]}}}
        )
        
        classify_response = ClassifyResponse(success=True, data="Test response")
        
        generate_query_response = GenerateQueryResponse(
            success=True,
            data='[{"$match": {"test": true}}]'
        )
        
        error_response = ErrorResponse(detail="Test error")
        
        # Test JSON serialization
        models = [
            classify_request,
            generate_query_request,
            classify_response,
            generate_query_response,
            error_response
        ]
        
        for model in models:
            # Should not raise exception
            json_str = model.model_dump_json()
            assert isinstance(json_str, str)
            assert len(json_str) > 0

    def test_schema_examples_valid(self):
        """Test that schema examples are valid."""
        # Test ClassifyRequest example
        classify_example = {"prompt": "What is the process for user authentication?"}
        classify_request = ClassifyRequest(**classify_example)
        assert classify_request.prompt == classify_example["prompt"]
        
        # Test GenerateQueryRequest example
        query_example = {
            "prompt": "Show me user logins by date",
            "query_type": "aggregation",
            "chart_type": "bar",
            "dbSchema": {
                "collections": {"users": {"fields": ["username", "login_date"]}}
            }
        }
        query_request = GenerateQueryRequest(**query_example)
        assert query_request.prompt == query_example["prompt"]
        
        # Test response examples
        classify_response_example = {
            "success": True,
            "data": {"database": "users_list", "chart_type": "bar"}
        }
        classify_response = ClassifyResponse(**classify_response_example)
        assert classify_response.success is True
        
        query_response_example = {
            "success": True,
            "data": "db.users.aggregate([{$group: {_id: '$login_date', count: {$sum: 1}}}])"
        }
        query_response = GenerateQueryResponse(**query_response_example)
        assert query_response.success is True
        
        error_example = {"success": False, "detail": "Invalid query format"}
        error_response = ErrorResponse(**error_example)
        assert error_response.success is False
