"""
Tests for utility services.
"""
import json
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException
from pymongo.errors import PyMongoError

from app.services.utils.get_instructions import get_instructions
from app.services.utils import redis_setup
from app.services.utils.remove_markdown import remove_markdown


@pytest.mark.unit
class TestGetInstructions:
    """Test cases for get_instructions utility."""

    @pytest.mark.asyncio
    async def test_get_instructions_success(self, mock_database):
        """Test successful instruction retrieval."""
        # Setup mock
        mock_collection = MagicMock()
        mock_collection.find_one = AsyncMock(return_value={
            "useCaseType": "test_prompt",
            "instructions": "Test instructions for SimplrOps classification"
        })
        mock_database.get_collection.return_value = mock_collection
        
        # Call the function
        result = await get_instructions(mock_database, "test_prompt")
        
        # Assertions
        assert result == "Test instructions for SimplrOps classification"
        mock_database.get_collection.assert_called_once_with("aiConfiguration")
        mock_collection.find_one.assert_called_once_with({"useCaseType": "test_prompt"})

    @pytest.mark.asyncio
    async def test_get_instructions_not_found(self, mock_database):
        """Test instruction retrieval when use case is not found."""
        # Setup mock to return None
        mock_collection = MagicMock()
        mock_collection.find_one = AsyncMock(return_value=None)
        mock_database.get_collection.return_value = mock_collection
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await get_instructions(mock_database, "nonexistent_prompt")
        
        assert exc_info.value.status_code == 404
        assert exc_info.value.detail == "Use case not found"

    @pytest.mark.asyncio
    async def test_get_instructions_missing_instructions_field(self, mock_database):
        """Test instruction retrieval when instructions field is missing."""
        # Setup mock to return document without instructions field
        mock_collection = MagicMock()
        mock_collection.find_one = AsyncMock(return_value={
            "useCaseType": "test_prompt"
            # Missing "instructions" field
        })
        mock_database.get_collection.return_value = mock_collection
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await get_instructions(mock_database, "test_prompt")
        
        assert exc_info.value.status_code == 404
        assert exc_info.value.detail == "Use case not found"

    @pytest.mark.asyncio
    async def test_get_instructions_database_error(self, mock_database):
        """Test instruction retrieval when database error occurs."""
        # Setup mock to raise PyMongoError
        mock_collection = MagicMock()
        mock_collection.find_one = AsyncMock(side_effect=PyMongoError("Database connection failed"))
        mock_database.get_collection.return_value = mock_collection
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await get_instructions(mock_database, "test_prompt")
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Internal server error"

    @pytest.mark.asyncio
    async def test_get_instructions_empty_instructions(self, mock_database):
        """Test instruction retrieval with empty instructions."""
        # Setup mock to return empty instructions
        mock_collection = MagicMock()
        mock_collection.find_one = AsyncMock(return_value={
            "useCaseType": "test_prompt",
            "instructions": ""
        })
        mock_database.get_collection.return_value = mock_collection
        
        # Call the function
        result = await get_instructions(mock_database, "test_prompt")
        
        # Should return empty string
        assert result == ""

    @pytest.mark.asyncio
    async def test_get_instructions_different_use_cases(self, mock_database):
        """Test instruction retrieval for different use case types."""
        # Setup mock
        mock_collection = MagicMock()
        
        # Test different use cases
        use_cases = {
            "merged_classification_prompt": "Classification instructions",
            "rag_prompt": "RAG instructions",
            "query_prompt": "Query generation instructions"
        }
        
        for use_case, expected_instructions in use_cases.items():
            mock_collection.find_one = AsyncMock(return_value={
                "useCaseType": use_case,
                "instructions": expected_instructions
            })
            mock_database.get_collection.return_value = mock_collection
            
            # Call the function
            result = await get_instructions(mock_database, use_case)
            
            # Assertions
            assert result == expected_instructions


@pytest.mark.unit
class TestRedisSetup:
    """Test cases for Redis setup utility."""

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_get_success(self, mock_redis_client):
        """Test successful Redis get operation."""
        # Setup mock
        test_data = [["question1", "answer1"], ["question2", "answer2"]]
        mock_redis_client.get.return_value = json.dumps(test_data)
        
        # Call the function
        result = redis_setup.get("test_key")
        
        # Assertions
        assert result == test_data
        mock_redis_client.get.assert_called_once_with("test_key")

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_get_none_value(self, mock_redis_client):
        """Test Redis get operation when key doesn't exist."""
        # Setup mock to return None
        mock_redis_client.get.return_value = None
        
        # Call the function
        result = redis_setup.get("nonexistent_key")
        
        # Assertions
        assert result is None
        mock_redis_client.get.assert_called_once_with("nonexistent_key")

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_get_invalid_json(self, mock_redis_client):
        """Test Redis get operation with invalid JSON."""
        # Setup mock to return invalid JSON
        mock_redis_client.get.return_value = "invalid json content"
        
        # Call the function
        result = redis_setup.get("test_key")
        
        # Assertions
        assert result is None
        mock_redis_client.get.assert_called_once_with("test_key")

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_set_success(self, mock_redis_client):
        """Test successful Redis set operation."""
        # Setup mock
        mock_redis_client.set.return_value = True
        
        # Test data
        test_data = [["question1", "answer1"], ["question2", "answer2"]]
        
        # Call the function
        redis_setup.set("test_key", test_data)
        
        # Assertions
        mock_redis_client.set.assert_called_once_with(
            "test_key", 
            json.dumps(test_data), 
            ex=1800
        )

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_set_complex_data(self, mock_redis_client):
        """Test Redis set operation with complex data."""
        # Setup mock
        mock_redis_client.set.return_value = True
        
        # Test data with complex structure
        test_data = {
            "user_id": "test_user",
            "history": [
                {"question": "What is SimplrOps?", "answer": "SimplrOps is..."},
                {"question": "How does it work?", "answer": "It works by..."}
            ],
            "metadata": {
                "timestamp": "2024-01-01T00:00:00Z",
                "session_id": "session_123"
            }
        }
        
        # Call the function
        redis_setup.set("complex_key", test_data)
        
        # Assertions
        mock_redis_client.set.assert_called_once_with(
            "complex_key", 
            json.dumps(test_data), 
            ex=1800
        )

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_delete_success(self, mock_redis_client):
        """Test successful Redis delete operation."""
        # Setup mock
        mock_redis_client.delete.return_value = 1
        
        # Call the function
        redis_setup.delete("test_key")
        
        # Assertions
        mock_redis_client.delete.assert_called_once_with("test_key")

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_delete_nonexistent_key(self, mock_redis_client):
        """Test Redis delete operation for nonexistent key."""
        # Setup mock
        mock_redis_client.delete.return_value = 0
        
        # Call the function
        redis_setup.delete("nonexistent_key")
        
        # Assertions
        mock_redis_client.delete.assert_called_once_with("nonexistent_key")

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_get_empty_string(self, mock_redis_client):
        """Test Redis get operation with empty string value."""
        # Setup mock to return empty string
        mock_redis_client.get.return_value = ""
        
        # Call the function
        result = redis_setup.get("empty_key")
        
        # Assertions
        assert result is None  # Empty string should be treated as None

    @patch('app.services.utils.redis_setup.redis_client')
    def test_redis_operations_with_user_history(self, mock_redis_client):
        """Test Redis operations with typical user history data."""
        # Test setting user history
        user_id = "user_123"
        history = [
            ["What is SimplrOps?", "SimplrOps is a platform..."],
            ["How do I access it?", "You can access it via..."]
        ]
        
        # Test set operation
        mock_redis_client.set.return_value = True
        redis_setup.set(user_id, history)
        
        # Test get operation
        mock_redis_client.get.return_value = json.dumps(history)
        result = redis_setup.get(user_id)
        
        # Assertions
        assert result == history
        mock_redis_client.set.assert_called_with(user_id, json.dumps(history), ex=1800)
        mock_redis_client.get.assert_called_with(user_id)


@pytest.mark.unit
class TestRemoveMarkdown:
    """Test cases for remove_markdown utility."""

    def test_remove_markdown_basic(self):
        """Test basic markdown removal."""
        # Test data with markdown
        data = "```markdown\nThis is markdown content\n```"
        result = remove_markdown(data)
        
        # Assertions
        assert result == "\nThis is markdown content\n"

    def test_remove_markdown_plaintext(self):
        """Test plaintext markdown removal."""
        # Test data with plaintext markdown
        data = "```plaintext\nThis is plaintext content\n```"
        result = remove_markdown(data)
        
        # Assertions
        assert result == "\nThis is plaintext content\n"

    def test_remove_markdown_docx(self):
        """Test docx markdown removal."""
        # Test data with docx markdown
        data = "```docx\nThis is docx content\n```"
        result = remove_markdown(data)
        
        # Assertions
        assert result == "\nThis is docx content\n"

    def test_remove_markdown_generic(self):
        """Test generic markdown removal."""
        # Test data with generic markdown
        data = "```\nThis is generic markdown content\n```"
        result = remove_markdown(data)
        
        # Assertions
        assert result == "\nThis is generic markdown content\n"

    def test_remove_markdown_multiple_types(self):
        """Test removal of multiple markdown types."""
        # Test data with multiple markdown types
        data = "```markdown\nMarkdown content\n```\n```plaintext\nPlaintext content\n```"
        result = remove_markdown(data)
        
        # Assertions
        assert result == "\nMarkdown content\n\n\nPlaintext content\n"

    def test_remove_markdown_no_markdown(self):
        """Test with content that has no markdown."""
        # Test data without markdown
        data = "This is just regular text content"
        result = remove_markdown(data)
        
        # Assertions
        assert result == data  # Should remain unchanged

    def test_remove_markdown_empty_string(self):
        """Test with empty string."""
        data = ""
        result = remove_markdown(data)
        
        # Assertions
        assert result == ""

    def test_remove_markdown_invalid_input(self):
        """Test with invalid input type."""
        # Test with non-string input
        with pytest.raises(ValueError) as exc_info:
            remove_markdown(123)
        
        assert str(exc_info.value) == "Input must be a string"

    def test_remove_markdown_none_input(self):
        """Test with None input."""
        # Test with None input
        with pytest.raises(ValueError) as exc_info:
            remove_markdown(None)
        
        assert str(exc_info.value) == "Input must be a string"

    def test_remove_markdown_complex_content(self):
        """Test with complex content containing markdown."""
        # Test data with complex content
        data = """
        Here is some content:
        ```markdown
        # SimplrOps Documentation
        This is a comprehensive guide.
        ```
        
        And some more content:
        ```plaintext
        Configuration steps:
        1. Step one
        2. Step two
        ```
        
        Final content without markdown.
        """
        result = remove_markdown(data)
        
        # Should remove all markdown markers
        assert "```markdown" not in result
        assert "```plaintext" not in result
        assert "```" not in result
        assert "# SimplrOps Documentation" in result
        assert "Configuration steps:" in result
