"""
Tests for the authentication service.
"""
import pytest
from unittest.mock import patch, <PERSON><PERSON>ock
from fastapi import HTT<PERSON>Exception
from fastapi.security import HTTPAuthorizationCredentials

from app.services.auth_service import verify_jwt_token, JWTBearer


@pytest.mark.unit
class TestAuthService:
    """Test cases for the authentication service."""

    @patch('app.services.auth_service.get_ssm_parameters')
    @patch('app.services.auth_service.jwt.decode')
    async def test_verify_jwt_token_success(self, mock_jwt_decode, mock_get_ssm_parameters):
        """Test successful JWT token verification."""
        # Setup mocks
        mock_get_ssm_parameters.return_value = {"jwtPublicKey": "test_public_key"}
        mock_jwt_decode.return_value = {
            "sub": "1234567890",
            "name": "<PERSON> Doe",
            "iat": 1516239022
        }
        
        # Test data
        token = "valid.jwt.token"
        
        # Call the function
        result = await verify_jwt_token(token)
        
        # Assertions
        assert result == {
            "sub": "1234567890",
            "name": "<PERSON>",
            "iat": 1516239022
        }
        
        # Verify mocks were called correctly
        mock_get_ssm_parameters.assert_called_once_with(["jwtPublicKey"])
        mock_jwt_decode.assert_called_once_with(
            token, 
            "test_public_key", 
            algorithms=["RS256"]
        )

    @patch('app.services.auth_service.get_ssm_parameters')
    @patch('app.services.auth_service.jwt.decode')
    async def test_verify_jwt_token_invalid_token(self, mock_jwt_decode, mock_get_ssm_parameters):
        """Test JWT token verification with invalid token."""
        # Setup mocks
        mock_get_ssm_parameters.return_value = {"jwtPublicKey": "test_public_key"}
        mock_jwt_decode.side_effect = Exception("Invalid token")
        
        # Test data
        token = "invalid.jwt.token"
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await verify_jwt_token(token)
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Token verification failed"
        assert exc_info.value.headers == {"WWW-Authenticate": "Bearer"}

    @patch('app.services.auth_service.get_ssm_parameters')
    async def test_verify_jwt_token_ssm_parameter_error(self, mock_get_ssm_parameters):
        """Test JWT token verification when SSM parameter retrieval fails."""
        # Setup mock to raise ValueError
        mock_get_ssm_parameters.side_effect = ValueError("SSM parameter not found")
        
        # Test data
        token = "valid.jwt.token"
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await verify_jwt_token(token)
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Failed to retrieve JWT public key"

    @patch('app.services.auth_service.get_ssm_parameters')
    @patch('app.services.auth_service.jwt.decode')
    async def test_verify_jwt_token_jwt_error(self, mock_jwt_decode, mock_get_ssm_parameters):
        """Test JWT token verification with JWT-specific error."""
        from jose import JWTError
        
        # Setup mocks
        mock_get_ssm_parameters.return_value = {"jwtPublicKey": "test_public_key"}
        mock_jwt_decode.side_effect = JWTError("Token expired")
        
        # Test data
        token = "expired.jwt.token"
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await verify_jwt_token(token)
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Invalid or expired token"
        assert exc_info.value.headers == {"WWW-Authenticate": "Bearer"}


@pytest.mark.unit
class TestJWTBearer:
    """Test cases for the JWTBearer class."""

    def test_jwt_bearer_initialization(self):
        """Test JWTBearer initialization."""
        # Test with default auto_error
        bearer = JWTBearer()
        assert bearer.auto_error is True
        
        # Test with custom auto_error
        bearer_no_auto_error = JWTBearer(auto_error=False)
        assert bearer_no_auto_error.auto_error is False

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_success(self, mock_verify_jwt_token):
        """Test successful JWTBearer call."""
        # Setup mocks
        mock_verify_jwt_token.return_value = {"sub": "1234567890", "name": "John Doe"}
        
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request and credentials
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid.jwt.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            result = await bearer(mock_request)
        
        # Assertions
        assert result == "valid.jwt.token"
        mock_verify_jwt_token.assert_called_once_with("valid.jwt.token")

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_invalid_scheme(self, mock_verify_jwt_token):
        """Test JWTBearer call with invalid authentication scheme."""
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request and credentials with invalid scheme
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Basic",  # Invalid scheme
            credentials="some.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            with pytest.raises(HTTPException) as exc_info:
                await bearer(mock_request)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == "Invalid authentication scheme"

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_token_verification_fails(self, mock_verify_jwt_token):
        """Test JWTBearer call when token verification fails."""
        # Setup mock to return False (verification failed)
        mock_verify_jwt_token.return_value = False
        
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request and credentials
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="invalid.jwt.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            with pytest.raises(HTTPException) as exc_info:
                await bearer(mock_request)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == "Invalid token or expired token"

    async def test_jwt_bearer_call_no_credentials(self):
        """Test JWTBearer call when no credentials are provided."""
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request
        mock_request = MagicMock()
        
        # Mock the parent __call__ method to return None (no credentials)
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=None):
            with pytest.raises(HTTPException) as exc_info:
                await bearer(mock_request)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == "Invalid authorization code"

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_verify_token_exception(self, mock_verify_jwt_token):
        """Test JWTBearer call when verify_jwt_token raises an exception."""
        # Setup mock to raise HTTPException
        mock_verify_jwt_token.side_effect = HTTPException(
            status_code=401,
            detail="Token expired"
        )
        
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request and credentials
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="expired.jwt.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            with pytest.raises(HTTPException) as exc_info:
                await bearer(mock_request)
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Token expired"

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_with_auto_error_false(self, mock_verify_jwt_token):
        """Test JWTBearer call with auto_error=False."""
        # Setup mock to return valid token data
        mock_verify_jwt_token.return_value = {"sub": "1234567890", "name": "John Doe"}
        
        # Create JWTBearer instance with auto_error=False
        bearer = JWTBearer(auto_error=False)
        
        # Mock request and credentials
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid.jwt.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            result = await bearer(mock_request)
        
        # Assertions
        assert result == "valid.jwt.token"
        mock_verify_jwt_token.assert_called_once_with("valid.jwt.token")

    @patch('app.services.auth_service.verify_jwt_token')
    async def test_jwt_bearer_call_with_valid_payload(self, mock_verify_jwt_token):
        """Test JWTBearer call with valid token payload."""
        # Setup mock to return valid payload
        valid_payload = {
            "sub": "1234567890",
            "name": "John Doe",
            "admin": True,
            "iat": 1516239022,
            "exp": 1516242622
        }
        mock_verify_jwt_token.return_value = valid_payload
        
        # Create JWTBearer instance
        bearer = JWTBearer()
        
        # Mock request and credentials
        mock_request = MagicMock()
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid.jwt.token"
        )
        
        # Mock the parent __call__ method
        with patch.object(bearer.__class__.__bases__[0], '__call__', return_value=mock_credentials):
            result = await bearer(mock_request)
        
        # Assertions
        assert result == "valid.jwt.token"
        mock_verify_jwt_token.assert_called_once_with("valid.jwt.token")


@pytest.mark.integration
class TestAuthServiceIntegration:
    """Integration tests for authentication service."""

    def test_jwt_bearer_with_fastapi_dependency(self):
        """Test JWTBearer as FastAPI dependency."""
        from fastapi import Depends
        
        # Create JWTBearer instance
        jwt_auth = JWTBearer()
        
        # Test that it can be used as a dependency
        def protected_endpoint(token: str = Depends(jwt_auth)):
            return {"message": "Protected endpoint accessed"}
        
        # Verify the dependency is properly configured
        assert callable(jwt_auth)
        assert hasattr(jwt_auth, '__call__')
