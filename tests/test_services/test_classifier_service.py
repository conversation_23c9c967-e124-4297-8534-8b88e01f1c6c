"""
Tests for the classifier service.
"""
import json
import pytest
from unittest.mock import patch, <PERSON><PERSON>ock
from fastapi import HTT<PERSON>Exception

from app.services.ollie_service.classifier_service import (
    classify_question,
    extract_json_content,
    correct_anchor_tags
)


@pytest.mark.unit
class TestClassifierService:
    """Test cases for the classifier service."""

    @patch('app.services.ollie_service.classifier_service.client')
    def test_classify_question_success(self, mock_client):
        """Test successful question classification."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"answer": "simplrops"}'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test classification instructions"
        question = "What is SimplrOps?"
        history = []
        
        # Call the function
        result = classify_question(instructions, question, history)
        
        # Assertions
        assert result == '{"answer": "simplrops"}'
        mock_client.chat.completions.create.assert_called_once()
        
        # Verify the call parameters
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]['model'] == 'simplrops-gpt-4o'
        assert call_args[1]['temperature'] == 0.3
        assert call_args[1]['top_p'] == 0.5
        assert call_args[1]['max_tokens'] == 1000

    @patch('app.services.ollie_service.classifier_service.client')
    def test_classify_question_with_history(self, mock_client):
        """Test question classification with conversation history."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"answer": "simplrops"}'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data with history
        instructions = "Test classification instructions"
        question = "Tell me more about it"
        history = [
            ["What is SimplrOps?", '{"answer": "simplrops"}'],
            ["How does it work?", "SimplrOps helps manage Workday configurations"]
        ]
        
        # Call the function
        result = classify_question(instructions, question, history)
        
        # Assertions
        assert result == '{"answer": "simplrops"}'
        
        # Verify the messages include history
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]['messages']
        
        # Should have system message + history + current question
        assert len(messages) == 6  # 1 system + 2*2 history + 1 current
        assert messages[0]['role'] == 'system'
        assert messages[1]['role'] == 'user'
        assert messages[2]['role'] == 'assistant'
        assert messages[-1]['role'] == 'user'
        assert messages[-1]['content'] == question

    @patch('app.services.ollie_service.classifier_service.client')
    def test_classify_question_api_error(self, mock_client):
        """Test error handling when OpenAI API fails."""
        # Setup mock to raise exception
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        # Test data
        instructions = "Test classification instructions"
        question = "What is SimplrOps?"
        history = []
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            classify_question(instructions, question, history)
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Classification service failed"

    def test_extract_json_content_valid_json(self):
        """Test extracting valid JSON content."""
        # Test with valid JSON
        content = '{"answer": "simplrops", "confidence": 0.95}'
        result = extract_json_content(content)
        
        assert result == {"answer": "simplrops", "confidence": 0.95}

    def test_extract_json_content_json_with_markdown(self):
        """Test extracting JSON content wrapped in markdown."""
        # Test with JSON wrapped in markdown
        content = '```json\n{"answer": "simplrops"}\n```'
        result = extract_json_content(content)
        
        assert result == {"answer": "simplrops"}

    def test_extract_json_content_invalid_json(self):
        """Test extracting invalid JSON content."""
        # Test with invalid JSON
        content = 'This is not JSON content'
        result = extract_json_content(content)
        
        assert result is None

    def test_extract_json_content_empty_string(self):
        """Test extracting from empty string."""
        content = ""
        result = extract_json_content(content)
        
        assert result is None

    def test_extract_json_content_none_input(self):
        """Test extracting from None input."""
        content = None
        result = extract_json_content(content)
        
        assert result is None

    def test_extract_json_content_partial_json(self):
        """Test extracting partial JSON content."""
        # Test with partial JSON that can be fixed
        content = '{"answer": "simplrops"'  # Missing closing brace
        result = extract_json_content(content)
        
        assert result is None  # Should return None for invalid JSON

    def test_correct_anchor_tags_basic_correction(self):
        """Test basic anchor tag correction."""
        # Test data with incorrect anchor tags
        data = 'Visit <a href="https://dev-customer1.simplrops.com/#/home/">Home Page</a>'
        result = correct_anchor_tags(data)
        
        expected = 'Visit <a href="https://dev-customer1.simplrops.com/#/home">Home Page</a>'
        assert result == expected

    def test_correct_anchor_tags_multiple_corrections(self):
        """Test multiple anchor tag corrections."""
        # Test data with multiple incorrect anchor tags
        data = '''
        <a href="https://dev-customer1.simplrops.com/#/home/">Home</a>
        <a href="https://dev-customer1.simplrops.com/#/config/">Config</a>
        '''
        result = correct_anchor_tags(data)
        
        assert 'href="https://dev-customer1.simplrops.com/#/home"' in result
        assert 'href="https://dev-customer1.simplrops.com/#/config"' in result
        assert '/#/home/"' not in result
        assert '/#/config/"' not in result

    def test_correct_anchor_tags_no_correction_needed(self):
        """Test anchor tag correction when no correction is needed."""
        # Test data with correct anchor tags
        data = 'Visit <a href="https://dev-customer1.simplrops.com/#/home">Home Page</a>'
        result = correct_anchor_tags(data)
        
        # Should remain unchanged
        assert result == data

    def test_correct_anchor_tags_no_anchor_tags(self):
        """Test anchor tag correction with no anchor tags."""
        # Test data without anchor tags
        data = 'This is just plain text without any links.'
        result = correct_anchor_tags(data)
        
        # Should remain unchanged
        assert result == data

    def test_correct_anchor_tags_empty_string(self):
        """Test anchor tag correction with empty string."""
        data = ""
        result = correct_anchor_tags(data)
        
        assert result == ""

    def test_correct_anchor_tags_complex_html(self):
        """Test anchor tag correction with complex HTML."""
        # Test data with complex HTML structure
        data = '''
        <div>
            <p>Welcome to SimplrOps!</p>
            <ul>
                <li><a href="https://dev-customer1.simplrops.com/#/dashboard/">Dashboard</a></li>
                <li><a href="https://dev-customer1.simplrops.com/#/reports/">Reports</a></li>
            </ul>
        </div>
        '''
        result = correct_anchor_tags(data)
        
        assert 'href="https://dev-customer1.simplrops.com/#/dashboard"' in result
        assert 'href="https://dev-customer1.simplrops.com/#/reports"' in result
        assert '/#/dashboard/"' not in result
        assert '/#/reports/"' not in result

    @patch('app.services.ollie_service.classifier_service.client')
    def test_classify_question_database_response(self, mock_client):
        """Test classification returning database response."""
        # Setup mock response for database classification
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"database": "securityGroups_list", "chart_type": "table"}'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test classification instructions"
        question = "Show me all security groups"
        history = []
        
        # Call the function
        result = classify_question(instructions, question, history)
        
        # Assertions
        assert result == '{"database": "securityGroups_list", "chart_type": "table"}'

    @patch('app.services.ollie_service.classifier_service.client')
    def test_classify_question_workday_response(self, mock_client):
        """Test classification returning Workday response."""
        # Setup mock response for Workday classification
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"answer": "Workday is an enterprise cloud application..."}'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test classification instructions"
        question = "What is Workday HCM?"
        history = []
        
        # Call the function
        result = classify_question(instructions, question, history)
        
        # Assertions
        assert result == '{"answer": "Workday is an enterprise cloud application..."}'

    def test_extract_json_content_nested_json(self):
        """Test extracting nested JSON content."""
        # Test with nested JSON
        content = '{"answer": "simplrops", "details": {"version": "1.0", "features": ["config", "reports"]}}'
        result = extract_json_content(content)
        
        expected = {
            "answer": "simplrops", 
            "details": {
                "version": "1.0", 
                "features": ["config", "reports"]
            }
        }
        assert result == expected
