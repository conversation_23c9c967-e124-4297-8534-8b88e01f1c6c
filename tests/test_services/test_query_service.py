"""
Tests for the query generation service.
"""
import json
import pytest
from unittest.mock import patch, MagicMock

from app.services.ollie_service.query_service import get_query, preprocess_query


@pytest.mark.unit
class TestQueryService:
    """Test cases for the query generation service."""

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_success(self, mock_client):
        """Test successful query generation."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {"isInactive": {"$ne": true}}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test query generation instructions"
        question = "Show me all active security groups"
        query_type = "list"
        chart_type = "table"
        schema = '{"collections": {"securityGroups": {"fields": ["simplropsId", "securityGroupName", "isInactive"]}}}'
        history = []
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        expected_query = '[{"$match": {"isInactive": {"$ne": true}}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1}}]'
        assert result == expected_query
        
        # Verify the call parameters
        mock_client.chat.completions.create.assert_called_once()
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]['model'] == 'simplrops-gpt-4o'
        assert call_args[1]['temperature'] == 0.2
        assert call_args[1]['max_tokens'] == 256

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_with_history(self, mock_client):
        """Test query generation with conversation history."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {"isInactive": {"$ne": true}, "securityGroupName": {"$regex": "Admin", "$options": "i"}}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data with history
        instructions = "Test query generation instructions"
        question = "Show me admin security groups"
        query_type = "list"
        chart_type = "table"
        schema = '{"collections": {"securityGroups": {"fields": ["simplropsId", "securityGroupName", "isInactive"]}}}'
        history = [
            ["Show me all security groups", '[{"$match": {}}]'],
            ["How many are active?", '[{"$match": {"isInactive": {"$ne": true}}}, {"$group": {"_id": null, "count": {"$sum": 1}}}]']
        ]
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        assert "Admin" in result
        assert "$regex" in result
        
        # Verify the messages include history
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]['messages']
        
        # Should have system message + history + current question
        assert len(messages) == 6  # 1 system + 2*2 history + 1 current
        assert messages[0]['role'] == 'system'
        assert messages[1]['role'] == 'user'
        assert messages[2]['role'] == 'assistant'

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_count_type(self, mock_client):
        """Test query generation for count type."""
        # Setup mock response for count query
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {"isInactive": {"$ne": true}}}, {"$group": {"_id": null, "count": {"$sum": 1}}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test query generation instructions"
        question = "How many active security groups are there?"
        query_type = "count"
        chart_type = None
        schema = '{"collections": {"securityGroups": {"fields": ["simplropsId", "securityGroupName", "isInactive"]}}}'
        history = []
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        assert "$group" in result
        assert "$sum" in result
        assert "count" in result

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_data_type(self, mock_client):
        """Test query generation for data type."""
        # Setup mock response for data query
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {"securityGroupName": "HR_Administrator"}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1, "policies": 1}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test query generation instructions"
        question = "Show me details of HR_Administrator security group"
        query_type = "data"
        chart_type = None
        schema = '{"collections": {"securityGroups": {"fields": ["simplropsId", "securityGroupName", "policies"]}}}'
        history = []
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        assert "HR_Administrator" in result
        assert "$match" in result
        assert "$project" in result

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_with_chart_type(self, mock_client):
        """Test query generation with specific chart type."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$group": {"_id": "$securityGroupType", "count": {"$sum": 1}}}, {"$sort": {"count": -1}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test query generation instructions"
        question = "Show me security groups by type"
        query_type = "list"
        chart_type = "bar"
        schema = '{"collections": {"securityGroups": {"fields": ["simplropsId", "securityGroupName", "securityGroupType"]}}}'
        history = []
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        assert "$group" in result
        assert "$sort" in result
        
        # Verify chart type was passed in the message
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]['messages']
        user_message = messages[-1]
        
        # The user message should contain chart type information
        assert any("bar" in str(content) for content in user_message['content'])

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_complex_schema(self, mock_client):
        """Test query generation with complex schema."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$lookup": {"from": "businessProcessEvents", "localField": "simplropsId", "foreignField": "securityGroups", "as": "processes"}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1, "processCount": {"$size": "$processes"}}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data with complex schema
        complex_schema = {
            "collections": {
                "securityGroups": {
                    "fields": ["simplropsId", "securityGroupName", "isInactive"],
                    "relationships": {
                        "processes": {
                            "type": "array",
                            "ref": "businessProcessEvents"
                        }
                    }
                },
                "businessProcessEvents": {
                    "fields": ["simplropsId", "businessProcessName", "securityGroups"]
                }
            }
        }
        
        instructions = "Test query generation instructions"
        question = "Show me security groups with their process counts"
        query_type = "list"
        chart_type = "table"
        schema = json.dumps(complex_schema)
        history = []
        
        # Call the function
        result = get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Assertions
        assert "$lookup" in result
        assert "$size" in result

    def test_preprocess_query_basic(self):
        """Test basic query preprocessing."""
        # Test data
        raw_query = '```json\n[{"$match": {"isInactive": {"$ne": true}}}]\n```'
        
        # Call the function
        result = preprocess_query(raw_query)
        
        # Assertions
        expected = '[{"$match": {"isInactive": {"$ne": true}}}]'
        assert result == expected

    def test_preprocess_query_with_backticks(self):
        """Test query preprocessing with backticks."""
        # Test data
        raw_query = '```[{"$match": {"isInactive": {"$ne": true}}}]```'
        
        # Call the function
        result = preprocess_query(raw_query)
        
        # Assertions
        expected = '[{"$match": {"isInactive": {"$ne": true}}}]'
        assert result == expected

    def test_preprocess_query_with_json_keyword(self):
        """Test query preprocessing with json keyword."""
        # Test data
        raw_query = 'json[{"$match": {"isInactive": {"$ne": true}}}]'
        
        # Call the function
        result = preprocess_query(raw_query)
        
        # Assertions
        expected = '[{"$match": {"isInactive": {"$ne": true}}}]'
        assert result == expected

    def test_preprocess_query_boolean_replacement(self):
        """Test query preprocessing with boolean replacement."""
        # Test data
        raw_query = '[{"$match": {"isInactive": {"$ne": True}, "isActive": False}}]'
        
        # Call the function
        result = preprocess_query(raw_query)
        
        # Assertions
        expected = '[{"$match": {"isInactive": {"$ne": True}, "isActive": False}}]'
        assert result == expected

    def test_preprocess_query_no_preprocessing_needed(self):
        """Test query preprocessing when no preprocessing is needed."""
        # Test data
        clean_query = '[{"$match": {"isInactive": {"$ne": true}}}]'
        
        # Call the function
        result = preprocess_query(clean_query)
        
        # Assertions
        assert result == clean_query

    def test_preprocess_query_empty_string(self):
        """Test query preprocessing with empty string."""
        # Test data
        empty_query = ""
        
        # Call the function
        result = preprocess_query(empty_query)
        
        # Assertions
        assert result == ""

    def test_preprocess_query_complex_cleaning(self):
        """Test query preprocessing with multiple cleaning operations."""
        # Test data
        complex_query = '```json\n[{"$match": {"isInactive": {"$ne": True}, "status": "active"}}, {"$project": {"_id": 0, "name": 1}}]\n```'
        
        # Call the function
        result = preprocess_query(complex_query)
        
        # Assertions
        expected = '[{"$match": {"isInactive": {"$ne": True}, "status": "active"}}, {"$project": {"_id": 0, "name": 1}}]'
        assert result == expected

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_api_error(self, mock_client):
        """Test query generation when API call fails."""
        # Setup mock to raise exception
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        # Test data
        instructions = "Test instructions"
        question = "Test question"
        query_type = "list"
        chart_type = "table"
        schema = '{"collections": {}}'
        history = []
        
        # Call the function and expect exception
        with pytest.raises(Exception):
            get_query(instructions, question, query_type, chart_type, schema, history)

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_message_structure(self, mock_client):
        """Test the structure of messages sent to the API."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test instructions"
        question = "Test question"
        query_type = "list"
        chart_type = "table"
        schema = '{"test": "schema"}'
        history = []
        
        # Call the function
        get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Verify message structure
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]['messages']
        
        # Should have system message and user message
        assert len(messages) == 2
        assert messages[0]['role'] == 'system'
        assert messages[0]['content'] == instructions
        
        assert messages[1]['role'] == 'user'
        user_content = messages[1]['content']
        
        # User message should contain all the input parameters
        assert len(user_content) == 4
        assert any(question in str(content) for content in user_content)
        assert any(query_type in str(content) for content in user_content)
        assert any(chart_type in str(content) for content in user_content)
        assert any(schema in str(content) for content in user_content)

    @patch('app.services.ollie_service.query_service.client')
    def test_get_query_api_parameters(self, mock_client):
        """Test API call parameters."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '[{"$match": {}}]'
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test data
        instructions = "Test instructions"
        question = "Test question"
        query_type = "list"
        chart_type = "table"
        schema = '{"test": "schema"}'
        history = []
        
        # Call the function
        get_query(instructions, question, query_type, chart_type, schema, history)
        
        # Verify API call parameters
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]['model'] == 'simplrops-gpt-4o'
        assert call_args[1]['temperature'] == 0.2
        assert call_args[1]['max_tokens'] == 256
        assert call_args[1]['top_p'] == 0.2
        assert call_args[1]['frequency_penalty'] == 0
        assert call_args[1]['presence_penalty'] == 0
        assert call_args[1]['response_format'] == {"type": "text"}

    def test_preprocess_query_whitespace_handling(self):
        """Test query preprocessing handles whitespace correctly."""
        # Test data with various whitespace
        queries_and_expected = [
            ('  [{"$match": {}}]  ', '[{"$match": {}}]'),
            ('\n[{"$match": {}}]\n', '[{"$match": {}}]'),
            ('\t[{"$match": {}}]\t', '[{"$match": {}}]'),
            ('```\n  [{"$match": {}}]  \n```', '[{"$match": {}}]'),
        ]
        
        for query, expected in queries_and_expected:
            result = preprocess_query(query)
            assert result.strip() == expected
