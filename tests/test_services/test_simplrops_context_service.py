"""
Tests for the SimplrOps context service.
"""
import pytest
from unittest.mock import patch, MagicMock

from app.services.ollie_service.simplrops_context_service_mongo import simplrops_context


@pytest.mark.unit
class TestSimplrOpsContextService:
    """Test cases for the SimplrOps context service."""

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', {})
    def test_simplrops_context_new_user(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test SimplrOps context for a new user without history."""
        # Setup mocks
        mock_history_aware_retriever = MagicMock()
        mock_create_history_aware_retriever.return_value = mock_history_aware_retriever

        mock_qa_chain = MagicMock()
        mock_create_stuff_documents_chain.return_value = mock_qa_chain

        mock_rag_chain = MagicMock()
        mock_rag_chain.invoke.return_value = {
            "answer": "SimplrOps is a comprehensive platform for managing Workday configurations."
        }
        mock_create_retrieval_chain.return_value = mock_rag_chain

        # Test data
        instructions = "Test RAG instructions"
        query = "What is SimplrOps?"
        user_id = "new_user_123"
        url = "https://test.simplrops.com"

        # Call the function
        result = simplrops_context(instructions, query, user_id, url)

        # Assertions
        assert result == "SimplrOps is a comprehensive platform for managing Workday configurations."

        # Verify RAG chain was invoked
        mock_rag_chain.invoke.assert_called_once()
        invoke_args = mock_rag_chain.invoke.call_args[0][0]
        assert invoke_args["input"] == query
        assert invoke_args["url"] == url

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    def test_simplrops_context_existing_user_with_history(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test SimplrOps context for an existing user with chat history."""
        # Setup mocks with existing history
        existing_history = [
            {"role": "user", "content": "What is SimplrOps?"},
            {"role": "assistant", "content": "SimplrOps is a platform..."}
        ]

        with patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', {"existing_user_456": existing_history}):
            mock_history_aware_retriever = MagicMock()
            mock_create_history_aware_retriever.return_value = mock_history_aware_retriever

            mock_qa_chain = MagicMock()
            mock_create_stuff_documents_chain.return_value = mock_qa_chain

            mock_rag_chain = MagicMock()
            mock_rag_chain.invoke.return_value = {
                "answer": "SimplrOps helps with tenant management and configuration validation."
            }
            mock_create_retrieval_chain.return_value = mock_rag_chain

            # Test data
            instructions = "Test RAG instructions"
            query = "How does SimplrOps help with tenant management?"
            user_id = "existing_user_456"
            url = "https://test.simplrops.com"

            # Call the function
            result = simplrops_context(instructions, query, user_id, url)

            # Assertions
            assert result == "SimplrOps helps with tenant management and configuration validation."

            # Verify RAG chain was invoked with history
            mock_rag_chain.invoke.assert_called_once()
            invoke_args = mock_rag_chain.invoke.call_args[0][0]
            assert invoke_args["input"] == query
            assert invoke_args["chat_history"] == existing_history

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history')
    def test_simplrops_context_history_limit(
        self,
        mock_chat_history,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test SimplrOps context with history limit (last 6 interactions)."""
        # Setup mocks with long history (more than 6 interactions)
        long_history = []
        for i in range(10):
            long_history.extend([
                {"role": "user", "content": f"Question {i}"},
                {"role": "assistant", "content": f"Answer {i}"}
            ])
        
        mock_chat_history.__getitem__ = MagicMock(return_value=long_history)
        mock_chat_history.__setitem__ = MagicMock()
        
        mock_history_aware_retriever = MagicMock()
        mock_create_history_aware_retriever.return_value = mock_history_aware_retriever
        
        mock_qa_chain = MagicMock()
        mock_create_stuff_documents_chain.return_value = mock_qa_chain
        
        mock_rag_chain = MagicMock()
        mock_rag_chain.invoke.return_value = {
            "answer": "Response with limited history context."
        }
        mock_create_retrieval_chain.return_value = mock_rag_chain
        
        # Test data
        instructions = "Test RAG instructions"
        query = "New question"
        user_id = "user_with_long_history"
        url = "https://test.simplrops.com"
        
        # Call the function
        result = simplrops_context(instructions, query, user_id, url)
        
        # Assertions
        assert result == "Response with limited history context."
        
        # Verify RAG chain was invoked
        mock_rag_chain.invoke.assert_called_once()
        invoke_args = mock_rag_chain.invoke.call_args[0][0]
        
        # Verify that only last 6 interactions were used (12 messages total)
        assert len(invoke_args["chat_history"]) == 12

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    def test_simplrops_context_updates_history(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test that SimplrOps context updates chat history after response."""
        # Setup mocks
        test_history = {}

        with patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', test_history):
            mock_history_aware_retriever = MagicMock()
            mock_create_history_aware_retriever.return_value = mock_history_aware_retriever

            mock_qa_chain = MagicMock()
            mock_create_stuff_documents_chain.return_value = mock_qa_chain

            mock_rag_chain = MagicMock()
            mock_rag_chain.invoke.return_value = {
                "answer": "This is the AI response."
            }
            mock_create_retrieval_chain.return_value = mock_rag_chain

            # Test data
            instructions = "Test RAG instructions"
            query = "Test question"
            user_id = "test_user"
            url = "https://test.simplrops.com"

            # Call the function
            result = simplrops_context(instructions, query, user_id, url)

            # Assertions
            assert result == "This is the AI response."

            # Verify that chat history was updated with new interaction
            assert user_id in test_history
            updated_history = test_history[user_id]

            # Should contain the new user message and AI response
            assert len(updated_history) == 2
            assert updated_history[0].content == query
            assert updated_history[1].content == "This is the AI response."

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', {})
    def test_simplrops_context_with_url_parameter(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test SimplrOps context with URL parameter passed to RAG chain."""
        # Setup mocks
        
        mock_history_aware_retriever = MagicMock()
        mock_create_history_aware_retriever.return_value = mock_history_aware_retriever
        
        mock_qa_chain = MagicMock()
        mock_create_stuff_documents_chain.return_value = mock_qa_chain
        
        mock_rag_chain = MagicMock()
        mock_rag_chain.invoke.return_value = {
            "answer": "URL-specific response."
        }
        mock_create_retrieval_chain.return_value = mock_rag_chain
        
        # Test data with specific URL
        instructions = "Test RAG instructions"
        query = "Show me the dashboard"
        user_id = "test_user"
        url = "https://dev-customer1.simplrops.com/#/dashboard"
        
        # Call the function
        result = simplrops_context(instructions, query, user_id, url)
        
        # Assertions
        assert result == "URL-specific response."
        
        # Verify that URL was passed to RAG chain
        mock_rag_chain.invoke.assert_called_once()
        invoke_args = mock_rag_chain.invoke.call_args[0][0]
        assert invoke_args["url"] == url

    @patch('app.services.ollie_service.simplrops_context_service_mongo.logger')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', {})
    def test_simplrops_context_logging(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain,
        mock_logger
    ):
        """Test that SimplrOps context includes proper logging."""
        # Setup mocks
        
        mock_history_aware_retriever = MagicMock()
        mock_create_history_aware_retriever.return_value = mock_history_aware_retriever
        
        mock_qa_chain = MagicMock()
        mock_create_stuff_documents_chain.return_value = mock_qa_chain
        
        mock_rag_chain = MagicMock()
        mock_rag_chain.invoke.return_value = {
            "answer": "Logged response."
        }
        mock_create_retrieval_chain.return_value = mock_rag_chain
        
        # Test data
        instructions = "Test RAG instructions"
        query = "Test question for logging"
        user_id = "test_user"
        url = "https://test.simplrops.com"
        
        # Call the function
        result = simplrops_context(instructions, query, user_id, url)
        
        # Assertions
        assert result == "Logged response."
        
        # Verify logging was called
        mock_logger.info.assert_called()

    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_stuff_documents_chain')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.data_retriever')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.llm')
    @patch('app.services.ollie_service.simplrops_context_service_mongo.chat_history', {})
    def test_simplrops_context_empty_query(
        self,
        mock_llm,
        mock_data_retriever,
        mock_create_history_aware_retriever,
        mock_create_stuff_documents_chain,
        mock_create_retrieval_chain
    ):
        """Test SimplrOps context with empty query."""
        # Setup mocks
        
        mock_history_aware_retriever = MagicMock()
        mock_create_history_aware_retriever.return_value = mock_history_aware_retriever
        
        mock_qa_chain = MagicMock()
        mock_create_stuff_documents_chain.return_value = mock_qa_chain
        
        mock_rag_chain = MagicMock()
        mock_rag_chain.invoke.return_value = {
            "answer": "Please provide a specific question about SimplrOps."
        }
        mock_create_retrieval_chain.return_value = mock_rag_chain
        
        # Test data with empty query
        instructions = "Test RAG instructions"
        query = ""
        user_id = "test_user"
        url = "https://test.simplrops.com"
        
        # Call the function
        result = simplrops_context(instructions, query, user_id, url)
        
        # Assertions
        assert result == "Please provide a specific question about SimplrOps."
        
        # Verify RAG chain was still invoked
        mock_rag_chain.invoke.assert_called_once()
        invoke_args = mock_rag_chain.invoke.call_args[0][0]
        assert invoke_args["input"] == ""
