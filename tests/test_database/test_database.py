"""
Tests for database module.
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from urllib.parse import urlparse
from pymongo.errors import PyMongoError
from motor.motor_asyncio import AsyncIOMotorClient

from app.database.database import (
    connect_to_mongo,
    close_mongo_connection,
    get_database,
    db,
    MongoDB
)


@pytest.mark.unit
class TestDatabaseModule:
    """Test cases for database module."""

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    @patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/test_db")
    async def test_connect_to_mongo_success(self, mock_async_motor_client):
        """Test successful MongoDB connection."""
        # Setup mocks
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function
        await connect_to_mongo()

        # Assertions
        assert db.client == mock_client
        assert db.database_name == "test_db"

        # Verify mocks were called
        mock_async_motor_client.assert_called_once_with("mongodb://localhost:27017/test_db")

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    @patch('app.database.database.MONGO_DETAILS', "mongodb+srv://testuser:<EMAIL>/production_db?retryWrites=true&w=majority")
    async def test_connect_to_mongo_complex_url(self, mock_async_motor_client):
        """Test MongoDB connection with complex URL."""
        # Setup mocks with complex MongoDB URL
        complex_url = "mongodb+srv://testuser:<EMAIL>/production_db?retryWrites=true&w=majority"
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function
        await connect_to_mongo()

        # Assertions
        assert db.client == mock_client
        assert db.database_name == "production_db"

        # Verify mocks were called
        mock_async_motor_client.assert_called_once_with(complex_url)

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    @patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/")
    async def test_connect_to_mongo_url_with_no_database(self, mock_async_motor_client):
        """Test MongoDB connection with URL that has no database name."""
        # Setup mocks with URL without database name
        url_no_db = "mongodb://localhost:27017/"
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function
        await connect_to_mongo()

        # Assertions
        assert db.client == mock_client
        assert db.database_name == ""  # Should be empty string

        # Verify mocks were called
        mock_async_motor_client.assert_called_once_with(url_no_db)

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    @patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/test_db")
    async def test_connect_to_mongo_connection_error(self, mock_async_motor_client):
        """Test MongoDB connection error handling."""
        # Setup mocks
        mock_async_motor_client.side_effect = PyMongoError("Connection failed")

        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function - should not raise exception but handle gracefully
        await connect_to_mongo()

        # Assertions - connection should have been attempted
        mock_async_motor_client.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.database.database.MONGO_DETAILS', None)
    async def test_connect_to_mongo_no_url(self):
        """Test MongoDB connection when no URL is available."""
        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function - should handle gracefully
        await connect_to_mongo()

        # Assertions - should not have set client
        assert db.client is None

    @pytest.mark.asyncio
    async def test_close_mongo_connection_with_client(self):
        """Test closing MongoDB connection when client exists."""
        # Setup mock client
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        db.client = mock_client
        
        # Call the function
        await close_mongo_connection()
        
        # Assertions
        mock_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_mongo_connection_no_client(self):
        """Test closing MongoDB connection when no client exists."""
        # Setup - no client
        db.client = None

        # Call the function - should not raise exception
        await close_mongo_connection()

        # Should complete without error
        assert db.client is None

    def test_get_database_with_client(self):
        """Test getting database when client exists."""
        # Setup mock client and database
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_database = MagicMock()
        mock_client.__getitem__.return_value = mock_database
        
        db.client = mock_client
        db.database_name = "test_db"
        
        # Call the function
        result = get_database()
        
        # Assertions
        assert result == mock_database
        mock_client.__getitem__.assert_called_once_with("test_db")

    def test_get_database_no_client(self):
        """Test getting database when no client exists."""
        # Setup - no client
        db.client = None
        db.database_name = "test_db"

        # Call the function - should raise TypeError
        with pytest.raises(TypeError):
            get_database()

    def test_get_database_no_database_name(self):
        """Test getting database when no database name is set."""
        # Setup mock client
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_database = MagicMock()
        mock_client.__getitem__.return_value = mock_database
        
        db.client = mock_client
        db.database_name = ""
        
        # Call the function
        result = get_database()
        
        # Assertions - should still work with empty string
        assert result == mock_database
        mock_client.__getitem__.assert_called_once_with("")

    def test_mongodb_class_initialization(self):
        """Test MongoDB class initialization."""
        # Create new instance
        mongodb_instance = MongoDB()
        
        # Assertions
        assert mongodb_instance.client is None
        assert mongodb_instance.database_name == ""

    def test_mongodb_class_attributes(self):
        """Test MongoDB class attributes."""
        # Test that class has expected attributes
        assert hasattr(MongoDB, 'client')
        assert hasattr(MongoDB, 'database_name')
        
        # Test default values
        mongodb_instance = MongoDB()
        assert mongodb_instance.client is None
        assert mongodb_instance.database_name == ""

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    async def test_connect_to_mongo_url_parsing(self, mock_async_motor_client):
        """Test URL parsing in connect_to_mongo function."""
        # Test various URL formats
        test_urls = [
            ("mongodb://localhost:27017/simple_db", "simple_db"),
            ("**************************************************", "auth_db"),
            ("mongodb+srv://testcluster.net/cloud_db", "cloud_db"),
            ("mongodb://localhost:27017/db_with_underscores", "db_with_underscores"),
            ("mongodb://localhost:27017/db-with-hyphens", "db-with-hyphens")
        ]

        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        for url, expected_db_name in test_urls:
            # Reset db state
            db.client = None
            db.database_name = ""

            # Patch MONGO_DETAILS for this test
            with patch('app.database.database.MONGO_DETAILS', url):
                # Call the function
                await connect_to_mongo()

                # Assertions
                assert db.database_name == expected_db_name

    @pytest.mark.asyncio
    @patch('app.database.database.AsyncIOMotorClient')
    async def test_connect_to_mongo_state_management(self, mock_async_motor_client):
        """Test state management in connect_to_mongo function."""
        # Setup mocks
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        # Verify initial state
        assert db.client is None or isinstance(db.client, AsyncIOMotorClient)

        # Call the function with first URL
        with patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/state_test_db"):
            await connect_to_mongo()

            # Verify state after connection
            assert db.client == mock_client
            assert db.database_name == "state_test_db"

        # Call again to test state update
        new_mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = new_mock_client

        with patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/new_db"):
            await connect_to_mongo()

            # Verify state was updated
            assert db.client == new_mock_client
            assert db.database_name == "new_db"

    def test_database_module_imports(self):
        """Test that all necessary imports are available."""
        # Test that required classes and functions are importable
        from app.database.database import (
            connect_to_mongo,
            close_mongo_connection,
            get_database,
            db,
            MongoDB
        )
        
        # Verify they are callable/instantiable
        assert callable(connect_to_mongo)
        assert callable(close_mongo_connection)
        assert callable(get_database)
        assert isinstance(db, MongoDB)
        assert callable(MongoDB)

    @pytest.mark.asyncio
    @patch('app.database.database.print')
    @patch('app.database.database.AsyncIOMotorClient')
    async def test_connect_to_mongo_logging(self, mock_async_motor_client, mock_print):
        """Test logging in connect_to_mongo function."""
        # Setup mocks
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        mock_async_motor_client.return_value = mock_client

        # Reset db state
        db.client = None
        db.database_name = ""

        # Call the function
        with patch('app.database.database.MONGO_DETAILS', "mongodb://localhost:27017/log_test_db"):
            await connect_to_mongo()

        # Verify logging occurred
        mock_print.assert_called()

        # Check that success message was printed
        print_calls = [call[0][0] for call in mock_print.call_args_list]
        success_messages = [msg for msg in print_calls if "Successfully connected" in msg]
        assert len(success_messages) > 0

    @pytest.mark.asyncio
    @patch('app.database.database.print')
    async def test_close_mongo_connection_logging(self, mock_print):
        """Test logging in close_mongo_connection function."""
        # Setup mock client
        mock_client = MagicMock(spec=AsyncIOMotorClient)
        db.client = mock_client
        
        # Call the function
        await close_mongo_connection()
        
        # Verify logging occurred
        mock_print.assert_called_with("MongoDB connection closed")
