"""
Shared pytest fixtures and configuration for SimplrOps Copilot tests.
"""
import asyncio
import os
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

# Add the app directory to the Python path
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    env_vars = {
        "ENVIRONMENT_NAME": "test",
        "AWS_REGION": "us-east-1",
        "LOCAL_DB_URL": "mongodb://localhost:27017/test_db",
        "redis_host": "localhost",
        "AZURE_OPENAI_API_KEY": "test_key",
        "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def mock_ssm_parameters():
    """Mock AWS SSM parameters."""
    return {
        "openaikey": "test_openai_key",
        "redispassword": "test_redis_password",
        "redishost": "localhost",
        "ai-model-endpoint": "https://test.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview",
        "embedding_azure_endpoint": "https://test.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15",
        "jwtPublicKey": "test_jwt_public_key",
        "central/dburl": "mongodb://localhost:27017/test_db"
    }


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    mock_client = MagicMock()
    
    # Mock chat completions
    mock_response = MagicMock()
    mock_response.choices = [MagicMock()]
    mock_response.choices[0].message.content = '{"answer": "simplrops"}'
    mock_client.chat.completions.create.return_value = mock_response
    
    return mock_client


@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    mock_redis = MagicMock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    return mock_redis


@pytest.fixture
def mock_mongodb_client():
    """Mock MongoDB client for testing."""
    mock_client = MagicMock()
    mock_db = MagicMock()
    mock_collection = MagicMock()
    
    # Setup async methods
    mock_collection.find_one = AsyncMock(return_value={
        "useCaseType": "test_prompt",
        "instructions": "Test instructions for SimplrOps"
    })
    
    mock_db.get_collection.return_value = mock_collection
    mock_client.__getitem__.return_value = mock_db
    
    return mock_client


@pytest.fixture
def mock_database():
    """Mock database dependency."""
    mock_db = MagicMock()
    mock_collection = MagicMock()
    
    # Mock aiConfiguration collection
    mock_collection.find_one = AsyncMock(return_value={
        "useCaseType": "merged_classification_prompt",
        "instructions": "Test classification instructions"
    })
    
    mock_db.get_collection.return_value = mock_collection
    return mock_db


@pytest.fixture
def sample_classify_request():
    """Sample classification request data."""
    return {
        "prompt": "What is SimplrOps?"
    }


@pytest.fixture
def sample_query_request():
    """Sample query generation request data."""
    return {
        "prompt": "Show me all active security groups",
        "query_type": "list",
        "chart_type": "table",
        "dbSchema": {
            "collections": {
                "securityGroups": {
                    "fields": ["simplropsId", "securityGroupName", "isInactive"]
                }
            }
        }
    }


@pytest.fixture
def sample_jwt_token():
    """Sample JWT token for testing."""
    return "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.test_signature"


@pytest.fixture
def mock_langchain_components():
    """Mock LangChain components for SimplrOps context service."""
    with patch('app.services.ollie_service.simplrops_context_service_mongo.MongoDBAtlasVectorSearch') as mock_vector_search, \
         patch('app.services.ollie_service.simplrops_context_service_mongo.AzureChatOpenAI') as mock_llm, \
         patch('app.services.ollie_service.simplrops_context_service_mongo.AzureOpenAIEmbeddings') as mock_embeddings, \
         patch('app.services.ollie_service.simplrops_context_service_mongo.create_history_aware_retriever') as mock_retriever, \
         patch('app.services.ollie_service.simplrops_context_service_mongo.create_retrieval_chain') as mock_rag_chain:
        
        # Mock vector search
        mock_vector_search.from_connection_string.return_value = MagicMock()
        
        # Mock LLM
        mock_llm_instance = MagicMock()
        mock_llm.return_value = mock_llm_instance
        
        # Mock embeddings
        mock_embeddings_instance = MagicMock()
        mock_embeddings.return_value = mock_embeddings_instance
        
        # Mock retriever
        mock_retriever.return_value = MagicMock()
        
        # Mock RAG chain
        mock_rag_chain_instance = MagicMock()
        mock_rag_chain_instance.invoke.return_value = {
            "answer": "SimplrOps is a comprehensive platform for managing Workday configurations."
        }
        mock_rag_chain.return_value = mock_rag_chain_instance
        
        yield {
            "vector_search": mock_vector_search,
            "llm": mock_llm,
            "embeddings": mock_embeddings,
            "retriever": mock_retriever,
            "rag_chain": mock_rag_chain
        }


@pytest.fixture
def client():
    """FastAPI test client."""
    try:
        from fastapi.testclient import TestClient
        from app.server import app
        return TestClient(app)
    except ImportError:
        # Return a mock client if imports fail
        return MagicMock()


@pytest.fixture
def authenticated_headers(sample_jwt_token):
    """Headers with authentication token."""
    return {
        "Authorization": f"Bearer {sample_jwt_token}",
        "Content-Type": "application/json",
        "userid": "test_user_123"
    }


@pytest.fixture
def sample_classification_response():
    """Sample classification response data."""
    return {
        "success": True,
        "data": "SimplrOps is a comprehensive platform for managing Workday configurations."
    }


@pytest.fixture
def sample_query_response():
    """Sample query generation response data."""
    return {
        "success": True,
        "data": '[{"$match": {"isInactive": {"$ne": true}}}, {"$project": {"_id": 0, "simplropsId": 1, "securityGroupName": 1}}]'
    }


# Additional fixtures for missing dependencies
@pytest.fixture
def mock_ssm_client():
    """Mock AWS SSM client."""
    mock_client = MagicMock()
    mock_client.get_parameter.return_value = {
        "Parameter": {"Name": "test_param", "Value": "test_value"}
    }
    return mock_client


@pytest.fixture
def mock_jwt_payload():
    """Mock JWT payload for testing."""
    return {
        "sub": "1234567890",
        "name": "John Doe",
        "admin": True,
        "iat": 1516239022,
        "exp": 1516242622
    }


@pytest.fixture
def mock_vector_search():
    """Mock MongoDB Atlas Vector Search."""
    mock_search = MagicMock()
    mock_search.similarity_search.return_value = [
        MagicMock(page_content="SimplrOps documentation content", metadata={"source": "docs"})
    ]
    return mock_search


@pytest.fixture
def mock_embeddings():
    """Mock Azure OpenAI embeddings."""
    mock_embeddings = MagicMock()
    mock_embeddings.embed_query.return_value = [0.1, 0.2, 0.3] * 512  # Mock embedding vector
    return mock_embeddings


@pytest.fixture
def mock_llm():
    """Mock Azure Chat OpenAI LLM."""
    mock_llm = MagicMock()
    mock_llm.invoke.return_value = MagicMock(content="Mocked LLM response")
    return mock_llm


@pytest.fixture
def sample_mongodb_document():
    """Sample MongoDB document for testing."""
    return {
        "_id": "507f1f77bcf86cd799439011",
        "useCaseType": "test_prompt",
        "instructions": "Test instructions for SimplrOps",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def sample_chat_history():
    """Sample chat history for testing."""
    return [
        {"role": "user", "content": "What is SimplrOps?"},
        {"role": "assistant", "content": "SimplrOps is a platform for managing Workday configurations."},
        {"role": "user", "content": "How does it work?"},
        {"role": "assistant", "content": "It provides tools for configuration management and validation."}
    ]


@pytest.fixture
def mock_cache():
    """Mock cache for testing."""
    cache_data = {}

    def mock_get(key):
        return cache_data.get(key)

    def mock_set(key, value):
        cache_data[key] = value
        return True

    def mock_delete(key):
        if key in cache_data:
            del cache_data[key]
        return True

    mock_cache = MagicMock()
    mock_cache.get.side_effect = mock_get
    mock_cache.set.side_effect = mock_set
    mock_cache.delete.side_effect = mock_delete

    return mock_cache
