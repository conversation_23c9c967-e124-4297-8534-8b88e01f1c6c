<?xml version="1.0" ?>
<coverage version="7.6.9" timestamp="1750136611444" lines-valid="624" lines-covered="520" line-rate="0.8333" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.6.9 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/home/<USER>/aspire/simplrOps/SimplrOps-github/copilot/app</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9714" branch-rate="0" complexity="0">
			<classes>
				<class name="server.py" filename="server.py" complexity="0" line-rate="0.9714" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
						<line number="57" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.v1" line-rate="0.9167" branch-rate="0" complexity="0">
			<classes>
				<class name="router.py" filename="api/v1/router.py" complexity="0" line-rate="0.9167" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="18" hits="1"/>
						<line number="23" hits="1"/>
						<line number="34" hits="1"/>
						<line number="47" hits="0"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.v1.controllers.ollie" line-rate="0.9052" branch-rate="0" complexity="0">
			<classes>
				<class name="classifier.py" filename="api/v1/controllers/ollie/classifier.py" complexity="0" line-rate="0.8971" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="34" hits="1"/>
						<line number="48" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="0"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1"/>
						<line number="121" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
					</lines>
				</class>
				<class name="query.py" filename="api/v1/controllers/ollie/query.py" complexity="0" line-rate="0.9167" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="33" hits="1"/>
						<line number="47" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="84" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="126" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.9341" branch-rate="0" complexity="0">
			<classes>
				<class name="config.py" filename="core/config.py" complexity="0" line-rate="0.9341" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="0"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="99" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="121" hits="1"/>
						<line number="125" hits="1"/>
						<line number="133" hits="1"/>
						<line number="139" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="157" hits="1"/>
						<line number="160" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="182" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="database" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="database.py" filename="database/database.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="65" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="models" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="schemas.py" filename="models/schemas.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services" line-rate="0.5172" branch-rate="0" complexity="0">
			<classes>
				<class name="auth_service.py" filename="services/auth_service.py" complexity="0" line-rate="0.5172" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="16" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="0"/>
						<line number="58" hits="1"/>
						<line number="59" hits="0"/>
						<line number="63" hits="1"/>
						<line number="65" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services.ollie_service" line-rate="0.7143" branch-rate="0" complexity="0">
			<classes>
				<class name="classifier_service.py" filename="services/ollie_service/classifier_service.py" complexity="0" line-rate="0.9783" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="19" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="578" hits="1"/>
						<line number="579" hits="1"/>
						<line number="580" hits="1"/>
						<line number="582" hits="1"/>
						<line number="583" hits="1"/>
						<line number="584" hits="1"/>
						<line number="585" hits="1"/>
						<line number="587" hits="1"/>
						<line number="594" hits="1"/>
						<line number="595" hits="1"/>
						<line number="596" hits="1"/>
						<line number="597" hits="1"/>
						<line number="603" hits="1"/>
						<line number="604" hits="1"/>
						<line number="605" hits="1"/>
						<line number="606" hits="1"/>
						<line number="607" hits="1"/>
						<line number="608" hits="1"/>
						<line number="610" hits="1"/>
						<line number="611" hits="1"/>
						<line number="612" hits="1"/>
						<line number="613" hits="1"/>
						<line number="615" hits="1"/>
						<line number="618" hits="1"/>
						<line number="627" hits="1"/>
						<line number="629" hits="1"/>
						<line number="630" hits="1"/>
						<line number="631" hits="1"/>
						<line number="632" hits="1"/>
						<line number="635" hits="1"/>
						<line number="636" hits="1"/>
						<line number="639" hits="1"/>
						<line number="640" hits="0"/>
						<line number="642" hits="1"/>
						<line number="644" hits="1"/>
						<line number="645" hits="1"/>
					</lines>
				</class>
				<class name="query_service.py" filename="services/ollie_service/query_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="10" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="265" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="286" hits="1"/>
						<line number="296" hits="1"/>
					</lines>
				</class>
				<class name="simplrops_context_service_chroma.py" filename="services/ollie_service/simplrops_context_service_chroma.py" complexity="0" line-rate="0.4268" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="51" hits="1"/>
						<line number="59" hits="1"/>
						<line number="68" hits="1"/>
						<line number="138" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="157" hits="1"/>
						<line number="165" hits="1"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="218" hits="0"/>
						<line number="227" hits="0"/>
						<line number="232" hits="0"/>
						<line number="241" hits="0"/>
						<line number="244" hits="0"/>
						<line number="249" hits="0"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="257" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="263" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="0"/>
						<line number="272" hits="0"/>
						<line number="274" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="287" hits="0"/>
					</lines>
				</class>
				<class name="simplrops_context_service_mongo.py" filename="services/ollie_service/simplrops_context_service_mongo.py" complexity="0" line-rate="0.7931" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="0"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="57" hits="1"/>
						<line number="62" hits="1"/>
						<line number="70" hits="1"/>
						<line number="79" hits="1"/>
						<line number="149" hits="1"/>
						<line number="158" hits="1"/>
						<line number="164" hits="1"/>
						<line number="174" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="207" hits="1"/>
						<line number="208" hits="1"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="1"/>
						<line number="226" hits="1"/>
						<line number="235" hits="1"/>
						<line number="240" hits="1"/>
						<line number="249" hits="1"/>
						<line number="252" hits="1"/>
						<line number="257" hits="1"/>
						<line number="262" hits="1"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="271" hits="1"/>
						<line number="276" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="280" hits="1"/>
						<line number="282" hits="1"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="287" hits="0"/>
						<line number="288" hits="0"/>
						<line number="289" hits="0"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services.utils" line-rate="0.9074" branch-rate="0" complexity="0">
			<classes>
				<class name="get_instructions.py" filename="services/utils/get_instructions.py" complexity="0" line-rate="0.8571" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
					</lines>
				</class>
				<class name="redis_setup.py" filename="services/utils/redis_setup.py" complexity="0" line-rate="0.92" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="14" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
					</lines>
				</class>
				<class name="remove_markdown.py" filename="services/utils/remove_markdown.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
