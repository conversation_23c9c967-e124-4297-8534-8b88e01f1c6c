import os
import tempfile
from typing import List, Optional
import uuid
import shutil

# <PERSON><PERSON>hain imports
from langchain.document_loaders import PyPDFLoader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.prompts import PromptTemplate
from langchain.vectorstores import Chroma
from langchain.llms import OpenAI
from langchain.chains import RetrievalQA
from langchain.chains.question_answering import load_qa_chain
from langchain.schema import Document

# ChromaDB imports
import chromadb
from chromadb.config import Settings


class DocumentQASystem:
    def __init__(self, openai_api_key: str):
        """Initialize the Document Q&A System"""
        # Set OpenAI API key
        os.environ["OPENAI_API_KEY"] = openai_api_key

        # ChromaDB configuration
        self.chroma_db_dir = "./chroma_db"
        os.makedirs(self.chroma_db_dir, exist_ok=True)

        # Initialize persistent ChromaDB client
        self.chroma_client = chromadb.PersistentClient(path=self.chroma_db_dir)

        # Storage for document collections
        self.document_collections = {}

        # Initialize embeddings and LLM
        self.embeddings = OpenAIEmbeddings()
        self.llm = OpenAI(api_key=openai_api_key)

        # Default prompt template
        self.default_prompt_template = """
        You are an AI agent, provide answer for the user question based on the context provided.
        {conversation_string}
        question: {question}

        context: {context}

        response: <answer>

        **note**:
        - Always respond with pure html format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
        """

    def load_document(self, file_path: str, file_type: str) -> List[Document]:
        """Load document based on file type"""
        if file_type == "pdf":
            loader = PyPDFLoader(file_path)
        elif file_type in ["docx", "doc"]:
            loader = Docx2txtLoader(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        return loader.load()

    def create_chroma_collection(self, documents: List[Document], collection_name: str) -> tuple:
        """Create ChromaDB collection from documents"""
        # Split documents into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )

        # Split the documents
        chunks = text_splitter.split_documents(documents)

        # Create ChromaDB vector store with persistent storage
        vector_store = Chroma.from_documents(
            documents=chunks,
            embedding=self.embeddings,
            collection_name=collection_name,
            persist_directory=self.chroma_db_dir
        )

        return vector_store, len(chunks)

    def get_chroma_collection(self, collection_name: str):
        """Get existing ChromaDB collection"""
        vector_store = Chroma(
            collection_name=collection_name,
            embedding_function=self.embeddings,
            persist_directory=self.chroma_db_dir
        )
        return vector_store

    def upload_document(self, file_path: str) -> dict:
        """
        Upload and process a document (PDF or DOCX) to create vector embeddings in ChromaDB
        """
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")

            # Get file extension
            filename = os.path.basename(file_path)
            file_extension = filename.split('.')[-1].lower()

            if file_extension not in ['pdf', 'docx', 'doc']:
                raise ValueError("Only PDF and DOCX files are supported")

            # Generate unique document ID and collection name
            document_id = str(uuid.uuid4())
            collection_name = f"doc_{document_id.replace('-', '_')}"

            print(f"Processing document: {filename}")

            # Load document
            documents = self.load_document(file_path, file_extension)

            # Add metadata to documents
            for doc in documents:
                doc.metadata.update({
                    "document_id": document_id,
                    "filename": filename,
                    "file_type": file_extension
                })

            # Create ChromaDB collection
            vector_store, chunks_count = self.create_chroma_collection(documents, collection_name)

            # Store mapping between document_id and collection_name
            self.document_collections[document_id] = {
                "collection_name": collection_name,
                "filename": filename,
                "chunks_count": chunks_count
            }

            result = {
                "document_id": document_id,
                "message": f"Document '{filename}' processed successfully. Created {chunks_count} chunks.",
                "chunks_count": chunks_count,
                "collection_name": collection_name
            }

            print(f"✅ {result['message']}")
            return result

        except Exception as e:
            raise Exception(f"Error processing document: {str(e)}")

    def create_custom_prompt(self, custom_prompt: str = None) -> PromptTemplate:
        """Create a custom prompt template"""
        template = custom_prompt if custom_prompt else self.default_prompt_template
        return PromptTemplate(
            input_variables=["context", "conversation_string", "question"],
            template=template
        )

    def ask_question(self, document_id: str, question: str, custom_prompt: str = None) -> dict:
        """
        Ask a question about a previously uploaded document
        """
        try:
            # Check if document exists
            if document_id not in self.document_collections:
                raise ValueError("Document not found. Please upload the document first.")

            # Get collection name for the document
            collection_info = self.document_collections[document_id]
            collection_name = collection_info["collection_name"]

            print(f"Asking question about document: {collection_info['filename']}")
            print(f"Question: {question}")

            # Get ChromaDB collection
            vector_store = self.get_chroma_collection(collection_name)

            # Create custom prompt template
            custom_prompt_template = self.create_custom_prompt(custom_prompt)

            # Get relevant documents
            docs = vector_store.similarity_search(question, k=1)

            # Create the stuff chain with our custom prompt
            qa_chain = load_qa_chain(
                llm=self.llm,
                chain_type="stuff",
                prompt=custom_prompt_template
            )

            # Call the chain with all required inputs
            result = qa_chain({
                "input_documents": docs,
                "question": question,
                "conversation_string": "",
                "context": "\n\n".join([doc.page_content for doc in docs])
            })

            # Extract source document content for reference
            source_docs = [doc.page_content[:200] + "..." for doc in docs]

            answer_result = {
                "answer": result.get("output_text", result.get("answer", result.get("result", "No answer generated"))),
                "source_documents": source_docs,
                "document_filename": collection_info['filename']
            }

            print(f"✅ Answer generated successfully")
            return answer_result

        except Exception as e:
            raise Exception(f"Error answering question: {str(e)}")

    def list_documents(self) -> dict:
        """List all uploaded documents"""
        return {
            doc_id: {
                "filename": info["filename"],
                "chunks_count": info["chunks_count"]
            }
            for doc_id, info in self.document_collections.items()
        }

    def delete_document(self, document_id: str) -> bool:
        """Delete a document and its collection"""
        try:
            if document_id not in self.document_collections:
                return False

            collection_name = self.document_collections[document_id]["collection_name"]

            # Delete from ChromaDB
            try:
                self.chroma_client.delete_collection(collection_name)
            except:
                pass  # Collection might not exist

            # Remove from memory
            del self.document_collections[document_id]
            return True
        except:
            return False


def main():
    """Example usage of the DocumentQASystem"""

    # Initialize the system with your OpenAI API key
    # Replace with your actual OpenAI API key
    API_KEY = "********************************************************************************************************************************************************************"

    qa_system = DocumentQASystem(openai_api_key=API_KEY)

    print("🚀 Document Q&A System Started")
    print("=" * 50)
    question = "what is in this document?"

    file_path = "harshit latest CV.pdf"
    try:
        result = qa_system.upload_document(file_path)
        print(f"Document ID: {result['document_id']}")
        document_id = result['document_id']
        result = qa_system.ask_question(document_id, question)
        print(f"❓ Question: {question}")
        print(f"🤖 Answer:{result['answer']}")
        print(f"\n📚 Source context (first 200 chars):\n{result['source_documents'][0] if result['source_documents'] else 'No source documents'}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()