import logging
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader
from langchain_community.vectorstores import Chroma
from langchain_openai import AzureOpenAIEmbeddings
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get environment name and region dynamically
env_name = os.environ.get("ENVIRONMENT_NAME")
region_name = os.environ.get("AWS_REGION")


def get_ssm_parameters(parameter_names):
    """
    Retrieves a list of parameters from AWS SSM.

    Args:
        parameter_names (list): List of parameter names.

    Returns:
        dict: Dictionary of parameter values.
    """
    try:
        ssm_client = boto3.client("ssm", region_name=region_name)
        value = {}

        for param in parameter_names:
            para = ssm_client.get_parameter(
                Name=f"/{env_name}/{param}", WithDecryption=True
            )
            value[param] = para["Parameter"]["Value"]
            logger.info(f"Fetched SSM Parameter: {param}")

        return value
    except (NoCredentialsError, PartialCredentialsError) as e:
        logger.error(f"Error fetching SSM parameters: {e}")
        raise
    except ssm_client.exceptions.ParameterNotFound:
        logger.error(f"SSM Parameter '{parameter_names}' not found")
        raise ValueError(f"SSM Parameter '{parameter_names}' not found.")


class ChromaVectorizer:
    def __init__(
        self,
        chroma_db_path: Optional[str] = None,
        collection_name: str = "SimplropsVectordata",
        chunk_size: int = 1000,
        chunk_overlap: int = 100,
    ):
        """Initialize the ChromaVectorizer with configuration parameters."""
        self.chroma_db_path = chroma_db_path or "./chroma_db"
        self.collection_name = collection_name
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.docs = []

        # Create ChromaDB directory if it doesn't exist
        os.makedirs(self.chroma_db_path, exist_ok=True)
        logger.info(f"ChromaDB path: {self.chroma_db_path}")

        self._initialize_embeddings()

    def _initialize_embeddings(self) -> None:
        """Initialize Azure OpenAI embeddings using SSM parameters."""
        try:
            # Get Azure OpenAI parameters from SSM
            params = get_ssm_parameters(
                ["openaikey", "embedding_azure_endpoint"]
            )

            # Parse the embedding endpoint URL to get deployment and version
            embedding_url = params["embedding_azure_endpoint"]
            embedding_deployment = embedding_url.split("/deployments/")[1].split("/")[0]
            embedding_version = embedding_url.split("api-version=")[1]

            # Get Azure endpoint base URL
            azure_base_url = "https://" + embedding_url.split("/")[2]

            logger.info(
                f"Using embedding deployment: {embedding_deployment}, version: {embedding_version}"
            )

            self.embeddings = AzureOpenAIEmbeddings(
                azure_deployment=embedding_deployment,
                openai_api_version=embedding_version,
                openai_api_key=params["openaikey"],
                azure_endpoint=azure_base_url,
            )
            logger.info("Successfully initialized Azure OpenAI embeddings")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
            raise

    def reset_collection(self) -> None:
        """Reset the ChromaDB collection by removing existing data."""
        try:
            collection_path = os.path.join(self.chroma_db_path, self.collection_name)
            if os.path.exists(collection_path):
                import shutil
                shutil.rmtree(collection_path)
                logger.info(f"Removed existing collection directory: {collection_path}")
            
            # Recreate the directory
            os.makedirs(self.chroma_db_path, exist_ok=True)
            logger.info(f"Reset collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error resetting collection '{self.collection_name}': {e}")
            raise

    def process_documents(
        self, docs_path: str, supported_extensions: List[str] = [".pdf", ".docx"]
    ) -> None:
        """Process documents from the specified path."""
        docs_dir = Path(docs_path)
        if not docs_dir.exists():
            raise ValueError(f"Directory not found: {docs_path}")

        logger.info(
            f"Scanning directory: {docs_path} for files with extensions: {supported_extensions}"
        )
        files = [
            f for f in docs_dir.rglob("*") if f.suffix.lower() in supported_extensions
        ]
        logger.info(f"Found {len(files)} documents to process")
        for ext in supported_extensions:
            count = sum(1 for f in files if f.suffix.lower() == ext)
            logger.info(f"  - {ext}: {count} files")

        for file_path in tqdm(files, desc="Processing documents"):
            try:
                logger.info(f"Processing file: {file_path}")
                if file_path.suffix.lower() == ".pdf":
                    logger.debug(f"Using PyPDFLoader for {file_path}")
                    loader = PyPDFLoader(str(file_path))
                elif file_path.suffix.lower() in [".docx", ".doc"]:
                    logger.debug(f"Using Docx2txtLoader for {file_path}")
                    loader = Docx2txtLoader(str(file_path))
                else:
                    logger.warning(f"Unsupported file type: {file_path}")
                    continue

                logger.debug("Loading document content")
                data = loader.load()
                logger.debug(
                    f"Splitting text with chunk_size={self.chunk_size}, chunk_overlap={self.chunk_overlap}"
                )
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=self.chunk_size, chunk_overlap=self.chunk_overlap
                )
                texts = text_splitter.split_documents(data)
                logger.debug(f"Generated {len(texts)} text chunks")

                for text in texts:
                    text.metadata.update(
                        {
                            "source": str(file_path),
                            "file_type": file_path.suffix,
                            "processed_date": str(datetime.now()),
                        }
                    )

                self.docs.extend(texts)
                logger.info(
                    f"Successfully processed {file_path} - Added {len(texts)} chunks to documents"
                )
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")

    def create_vector_search(self) -> None:
        """Create ChromaDB vector search index."""
        try:
            if not self.docs:
                logger.error("No documents available to create vector search index")
                raise ValueError("No documents to process")

            logger.info(
                f"Creating ChromaDB vector search with {len(self.docs)} documents"
            )
            
            self.vectorstore = Chroma.from_documents(
                documents=self.docs,
                embedding=self.embeddings,
                collection_name=self.collection_name,
                persist_directory=self.chroma_db_path,
            )

            logger.info(
                f"Successfully created ChromaDB vector search with collection '{self.collection_name}'"
            )
        except Exception as e:
            logger.error(f"Unexpected error creating vector search: {e}")
            raise

    def get_vectorstore(self):
        """Get the existing vectorstore."""
        if hasattr(self, 'vectorstore'):
            return self.vectorstore
        else:
            # Load existing vectorstore
            self.vectorstore = Chroma(
                collection_name=self.collection_name,
                embedding_function=self.embeddings,
                persist_directory=self.chroma_db_path,
            )
            return self.vectorstore


if __name__ == "__main__":
    try:
        logger.info("Initializing ChromaVectorizer...")
        vectorizer = ChromaVectorizer()

        logger.info("Resetting collection...")
        vectorizer.reset_collection()

        docs_path = "./docs"
        logger.info(f"Processing documents from: {docs_path}")
        vectorizer.process_documents(docs_path)

        logger.info("Creating vector search...")
        vectorizer.create_vector_search()

        logger.info("Process completed successfully")
    except Exception as e:
        logger.error(f"Application error: {e}")
