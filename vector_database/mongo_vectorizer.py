import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader, UnstructuredFileLoader
from langchain_mongodb import MongoDBAtlasVectorSearch
from langchain_openai import AzureOpenAIEmbeddings
from pymongo import MongoClient
from pymongo.errors import OperationFailure, PyMongoError
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get environment name and region dynamically
env_name = os.environ.get("ENVIRONMENT_NAME")
region_name = os.environ.get("AWS_REGION")


def get_database_url():
    """
    Retrieves the MongoDB URL from the environment or AWS SSM.

    Returns:
        str: The MongoDB connection URL.
    """
    try:
        local_db_url = os.environ.get("LOCAL_DB_URL")
        if local_db_url:
            logger.info(f"Using LOCAL_DB_URL from environment: {local_db_url}")
            return local_db_url

        # Fetch from AWS SSM
        db_key = "central/dburl"
        parameters = get_mongo_ssm_parameters([db_key])
        if db_key in parameters:
            logger.info("Fetched MongoDB URL from SSM")
            return parameters[db_key]

        raise ValueError("MongoDB URL could not be retrieved from environment or SSM")
    except Exception as e:
        logger.error(f"Error retrieving database URL: {e}")
        raise


def get_mongo_ssm_parameters(parameter_names):
    """
    Retrieves a list of parameters from AWS SSM.

    Args:
        parameter_names (list): List of parameter names.

    Returns:
        dict: Dictionary of parameter values.
    """
    try:
        ssm_client = boto3.client("ssm", region_name=region_name)
        value = {}

        for param in parameter_names:
            para = ssm_client.get_parameter(
                Name=f"/{env_name}/{param}", WithDecryption=True
            )
            value[param] = para["Parameter"]["Value"]
            logger.info(f"Fetched SSM Parameter: {param} -> {value[param]}")

        return value
    except (NoCredentialsError, PartialCredentialsError) as e:
        logger.error(f"Error fetching SSM parameters: {e}")
        raise
    except ssm_client.exceptions.ParameterNotFound:
        logger.error(f"SSM Parameter '{parameter_names}' not found")
        raise ValueError(f"SSM Parameter '{parameter_names}' not found.")


class MongoVectorizer:
    def __init__(
        self,
        db_url: Optional[str] = None,
        db_name: Optional[str] = None,
        collection_name: str = "SimplropsNewVectordata",
        chunk_size: int = 1000,
        chunk_overlap: int = 100,
    ):
        """Initialize the MongoVectorizer with configuration parameters."""
        self.db_url = db_url or get_database_url()

        if db_name is None:
            from urllib.parse import urlparse

            parsed_url = urlparse(self.db_url)
            self.db_name = parsed_url.path.lstrip("/")
            logger.info(f"Extracted database name from URL: {self.db_name}")
            if not self.db_name:
                logger.error("Failed to extract database name from MongoDB URL")
                raise ValueError(
                    "Database name could not be extracted from MongoDB URL"
                )
        else:
            logger.info(f"Using provided database name: {db_name}")
            self.db_name = db_name

        self.collection_name = collection_name
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.vector_search_index = "vector_index"
        self.docs = []

        self._initialize_mongo_connection()
        self._initialize_embeddings()

    def _initialize_mongo_connection(self) -> None:
        """Initialize MongoDB connection."""
        try:
            self.client = MongoClient(self.db_url)
            self.db = self.client[self.db_name]
            self.atlas_collection = self.db[self.collection_name]

            # Test connection
            self.client.admin.command("ping")
            logger.info("Successfully connected to MongoDB")

            # Create a regular index for the embedding field
            self.atlas_collection.create_index(
                [("embedding", "2d")], name=self.vector_search_index
            )
            logger.info(
                f"Created/Verified index '{self.vector_search_index}' for vector search"
            )

        except PyMongoError as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise

    def _initialize_embeddings(self) -> None:
        """Initialize Azure OpenAI embeddings securely using environment variables."""
        try:
            self.embeddings = AzureOpenAIEmbeddings(
                azure_deployment="text-embedding-3-small",
                openai_api_version="2023-05-15",
                openai_api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            )
            logger.info("Successfully initialized Azure OpenAI embeddings")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
            raise

    def reset_collection(self) -> None:
        """Reset the MongoDB collection."""
        try:
            if self.collection_name in self.db.list_collection_names():
                logger.info(f"Collection '{self.collection_name}' exists. Deleting...")
                self.db[self.collection_name].drop()
                logger.info(
                    f"Collection '{self.collection_name}' deleted successfully."
                )

            self.atlas_collection = self.db[self.collection_name]
            logger.info(f"Created new collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error resetting collection '{self.collection_name}': {e}")
            raise

    def process_documents(
        self, docs_path: str, supported_extensions: List[str] = [".pdf", ".txt", ".md"]
    ) -> None:
        """Process documents from the specified path."""
        docs_dir = Path(docs_path)
        if not docs_dir.exists():
            raise ValueError(f"Directory not found: {docs_path}")

        logger.info(
            f"Scanning directory: {docs_path} for files with extensions: {supported_extensions}"
        )
        files = [
            f for f in docs_dir.rglob("*") if f.suffix.lower() in supported_extensions
        ]
        logger.info(f"Found {len(files)} documents to process")
        for ext in supported_extensions:
            count = sum(1 for f in files if f.suffix.lower() == ext)
            logger.info(f"  - {ext}: {count} files")

        for file_path in tqdm(files, desc="Processing documents"):
            try:
                logger.info(f"Processing file: {file_path}")
                if file_path.suffix.lower() == ".pdf":
                    logger.debug(f"Using PyPDFLoader for {file_path}")
                    loader = PyPDFLoader(str(file_path))
                else:
                    logger.debug(f"Using UnstructuredFileLoader for {file_path}")
                    loader = UnstructuredFileLoader(str(file_path))

                logger.debug("Loading document content")
                data = loader.load()
                logger.debug(
                    f"Splitting text with chunk_size={self.chunk_size}, chunk_overlap={self.chunk_overlap}"
                )
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=self.chunk_size, chunk_overlap=self.chunk_overlap
                )
                texts = text_splitter.split_documents(data)
                logger.debug(f"Generated {len(texts)} text chunks")

                for text in texts:
                    text.metadata.update(
                        {
                            "source": str(file_path),
                            "file_type": file_path.suffix,
                            "processed_date": str(datetime.now()),
                        }
                    )

                self.docs.extend(texts)
                logger.info(
                    f"Successfully processed {file_path} - Added {len(texts)} chunks to documents"
                )
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")

    def create_vector_search(self) -> None:
        """Create vector search index."""
        try:
            if not self.docs:
                logger.error("No documents available to create vector search index")
                raise ValueError("No documents to process")

            logger.info(
                f"Creating vector search index '{self.vector_search_index}' with {len(self.docs)} documents"
            )
            self.docsearch = MongoDBAtlasVectorSearch.from_documents(
                self.docs,
                self.embeddings,
                collection=self.atlas_collection,
                index_name=self.vector_search_index,
            )

            logger.info(
                f"Successfully created vector search index '{self.vector_search_index}'"
            )
        except OperationFailure as e:
            logger.error(f"Failed to create search index: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating vector search: {e}")
            raise

    def cleanup(self) -> None:
        """Cleanup MongoDB connection."""
        try:
            if hasattr(self, "client"):
                self.client.close()
                logger.info("Successfully closed MongoDB connection")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


if __name__ == "__main__":
    vectorizer = None
    try:
        logger.info("Initializing MongoVectorizer...")
        vectorizer = MongoVectorizer()

        logger.info("Resetting collection...")
        vectorizer.reset_collection()

        docs_path = "/home/<USER>/aspire/simplrOps/SimplrOps-github/copilot/docs"
        logger.info(f"Processing documents from: {docs_path}")
        vectorizer.process_documents(docs_path)

        logger.info("Creating vector search...")
        vectorizer.create_vector_search()

        logger.info("Process completed successfully")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        if vectorizer:
            vectorizer.cleanup()
