# MongoDB Document Vectorizer

This tool processes documents (PDF, TXT, MD) and creates vector embeddings stored in MongoDB Atlas for efficient semantic search capabilities.

## Prerequisites

1. Python 3.8 or higher
2. MongoDB Atlas account (or local MongoDB instance)
3. Azure OpenAI API access
4. (Optional) AWS account with SSM access

## Installation

1. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Linux/Mac
# OR
.\venv\Scripts\activate  # On Windows
```

2. Install required dependencies:
```bash
pip install boto3 python-dotenv pymongo[aws] langchain langchain_mongodb langchain_openai tqdm pypdf unstructured
```

## Configuration

1. Create a `.env` file in the same directory as the script with the following variables:

```env
# MongoDB Configuration
# Option 1: Local MongoDB
LOCAL_DB_URL=mongodb://localhost:27017/your_database

# Option 2: AWS SSM (if not using LOCAL_DB_URL)
ENVIRONMENT_NAME=your_env_name
AWS_REGION=your_aws_region

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
```

2. If using AWS SSM:
   - Ensure AWS credentials are configured (`~/.aws/credentials` or environment variables)
   - Store MongoDB URL in SSM parameter store as `/{ENVIRONMENT_NAME}/central/dburl`

## Usage

1. Prepare your documents:
   - Create a folder to store your documents
   - Supported formats: PDF, TXT, MD
   - Example structure:
     ```
     documents/
     ├── doc1.pdf
     ├── doc2.txt
     └── subfolder/
         └── doc3.md
     ```

2. Update the script:
   - Open `mongo_vectorizer.py`
   - Change `"document-folder-path"` to your documents folder path:
     ```python
     vectorizer.process_documents("path/to/your/documents")
     ```

3. Run the script:
```bash
python mongo_vectorizer.py
```

## Process Flow

1. The script will:
   - Connect to MongoDB using the configured URL
   - Scan the specified directory for supported documents
   - Process each document:
     - Load and split into chunks
     - Generate embeddings using Azure OpenAI
     - Store in MongoDB with metadata
   - Create a vector search index

## Logging

The script provides detailed logging at different levels:
- INFO: Major operations and state changes
- DEBUG: Detailed process information
- ERROR: Error conditions and exceptions

Logs include:
- Document processing progress
- Chunk generation statistics
- Vector index creation status
- Any errors or issues encountered

## Troubleshooting

1. MongoDB Connection Issues:
   - Verify MongoDB URL is correct
   - Check network connectivity
   - Ensure proper authentication credentials

2. AWS SSM Issues:
   - Verify AWS credentials
   - Check parameter exists in SSM
   - Confirm correct environment name and region

3. Document Processing Issues:
   - Verify file permissions
   - Check supported file formats
   - Ensure documents are readable

4. Azure OpenAI Issues:
   - Verify API key and endpoint
   - Check API quotas and limits
   - Ensure proper authentication

## Security Notes

- Never commit `.env` file or sensitive credentials
- Use appropriate access controls for AWS and MongoDB
- Follow principle of least privilege for all service accounts
- Regularly rotate API keys and credentials
