from urllib.parse import urlparse

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import PyMongoError

from app.core.config import get_database_url

# Include the database name in the URL
MONGO_DETAILS = get_database_url()


class MongoDB:
    client: AsyncIOMotorClient = None
    database_name: str = ""


db = MongoDB()


async def connect_to_mongo():
    """
    Establishes a connection to MongoDB using the MONGO_DETAILS environment variable.

    When a connection is successfully established, the database name is extracted from the URL and stored.
    """
    try:
        if MONGO_DETAILS is None:
            print("No MongoDB URL provided")
            return

        print("Connecting to MongoDB")
        db.client = AsyncIOMotorClient(MONGO_DETAILS)
        parsed_url = urlparse(MONGO_DETAILS)
        print(parsed_url)
        db.database_name = parsed_url.path.strip("/")
        print(
            "Successfully connected to MongoDB, database: {}".format(db.database_name)
        )
    except PyMongoError as e:
        print("Failed to connect to MongoDB: {}".format(e))


async def close_mongo_connection():
    """Close the MongoDB connection.

    This function is a coroutine and should be run using `await`.

    """
    if db.client is not None:
        db.client.close()
        print("MongoDB connection closed")


def get_database():
    """
    Return the MongoDB database instance.

    This function returns the MongoDB database instance for the
    currently connected database.

    Returns:
        motor.motor_asyncio.AsyncIOMotorDatabase: The MongoDB database instance.

    """
    return db.client[db.database_name]
