import json
import logging

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Request
from pydantic import BaseModel

from app.database.database import get_database
from app.models.schemas import (
    ErrorResponse,
    GenerateQueryRequest,
    GenerateQueryResponse,
)
from app.services.auth_service import JWTBearer
from app.services.ollie_service.query_service import get_query, preprocess_query
from app.services.utils import redis_setup as cache
from app.services.utils.get_instructions import get_instructions

logger = logging.getLogger(__name__)

# JWT auth instance
jwt_auth = JWTBearer()

router = APIRouter(
    tags=["ollie"],
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
)


@router.post(
    "/generate_query",
    response_model=GenerateQueryResponse,
    responses={
        200: {
            "model": GenerateQueryResponse,
            "description": "Successfully generated query",
        },
        401: {"description": "Unauthorized - Invalid or missing token"},
        403: {"description": "Forbidden - Invalid authentication scheme"},
        422: {"description": "Validation Error"},
    },
    dependencies=[Depends(JWTBearer())],
)
async def generate_query(
    request: Request,
    request_data: GenerateQueryRequest,
    database=Depends(get_database),
):
    """
    Generate a MongoDB query based on the given prompt and parameters.

    Args:
        request: The FastAPI request object for headers and metadata
        request_data: The validated request body containing prompt and query parameters
        database: The MongoDB database instance
        token: JWT token for authentication

    Returns:
        GenerateQueryResponse: Success status and generated query

    Raises:
        HTTPException: If query generation fails
    """
    try:
        # Get user ID from request headers
        user_id = request.headers.get("userid", "unused")
        logger.info(f"User ID: {user_id}")

        # Log validated request data
        logger.info(
            f"Received Request Data: {json.dumps(request_data.dict(), indent=2)}"
        )

        # Get query generation instructions
        instructions = await get_instructions(database, "query_prompt")
        logger.info(
            f"Retrieved Instructions: {instructions[:200]}..."
        )  # Log first 200 chars

        # Log query parameters
        logger.info(
            f"Processing Query - Question: {request_data.prompt}, "
            f"Query Type: {request_data.query_type}, "
            f"Chart Type: {request_data.chart_type}"
        )

        # Get user history from cache
        try:
            history = cache.get(user_id)
            if history is None:
                history = []
                cache.set(user_id, history)
            logger.info(
                f"History for User ({user_id}): {history[-3:] if history else 'No history'}"
            )
        except Exception as e:
            logger.warning(f"Failed to get history from cache: {e}")
            history = []

        # Generate and process query
        query = get_query(
            instructions,
            request_data.prompt,
            request_data.query_type,
            request_data.chart_type,
            json.dumps(request_data.dbSchema),
            history[-3:],
        )
        mongo_query = preprocess_query(query)
        logger.info(f"Generated MongoDB Query: {mongo_query}")

        # Update history
        try:
            history.append([request_data.prompt, mongo_query])
            cache.set(user_id, history[-3:])
        except Exception as e:
            logger.warning(f"Failed to update history in cache: {e}")

        # Prepare response - keep mongo_query as string without parsing to JSON
        response = GenerateQueryResponse(success=True, data=mongo_query)
        logger.info(f"Response Sent: {json.dumps(response.model_dump(), indent=2)}")

        return response

    except ValueError as ve:
        logger.error(f"Value Error: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal Server Error")
