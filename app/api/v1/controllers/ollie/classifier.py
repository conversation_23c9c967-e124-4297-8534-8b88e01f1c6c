import json
import logging
import time

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import J<PERSON>NResponse

from app.database.database import get_database
from app.models.schemas import ClassifyRequest, ClassifyResponse
from app.services.auth_service import JWTBearer
from app.services.ollie_service.classifier_service import (
    classify_question,
    correct_anchor_tags,
    extract_json_content,
)
# from app.services.ollie_service.simplrops_context_service_mongo import simplrops_context
from app.services.ollie_service.simplrops_context_service_chroma import simplrops_context
from app.services.utils import redis_setup as cache
from app.services.utils.get_instructions import get_instructions

logger = logging.getLogger(__name__)

jwt_auth = JWTBearer()

router = APIRouter(
    tags=["ollie"],
    responses={
        500: {"description": "Internal Server Error"},
        400: {"description": "Bad Request"},
    },
)


@router.post(
    "/classify",
    response_model=ClassifyResponse,
    responses={
        200: {
            "model": ClassifyResponse,
            "description": "Successfully classified the query",
        },
        401: {"description": "Unauthorized - Invalid or missing token"},
        403: {"description": "Forbidden - Invalid authentication scheme"},
        422: {"description": "Validation Error"},
    },
    dependencies=[Depends(JWTBearer())],
)
async def query_classification(
    request: Request,
    classify_request: ClassifyRequest,
    database=Depends(get_database),
):
    """
    Query the classification based on the given request and database.
    Parameters:
    - request (Request): The request object containing headers.
    - classify_request (ClassifyRequest): The validated request body.
    - database (Depends): The database dependency.
    Returns:
    - ClassifyResponse: A response containing the classification result.
    Raises:
    - ValueError: If the database_type format is invalid.
    - HTTPException: If there is a value error or unexpected error.
    """
    try:
        # Get classification instructions
        classify_instructions = await get_instructions(
            database, "merged_classification_prompt"
        )
        simplrops_instructions = await get_instructions(database, "rag_prompt")

        # Get user ID from headers
        head = request.headers
        user_id = head.get("userid", "unused")
        url = head.get("referer", "") + "#"
        logger.info(f"user id: {user_id}")

        question = classify_request.prompt
        logger.info(f"question: {question}")

        # Redis history handling
        history = cache.get(user_id)
        if history is None:
            history = []
            cache.set(user_id, history)

        # Classify the question
        question_response = classify_question(
            classify_instructions, question, history[-3:]
        )
        logger.info(f"question type: {question_response}")
        classify_result = extract_json_content(question_response)

        if classify_result is None:
            logger.debug(f"Question type: {question_response}")
            history.append([question, json.dumps(question_response)])
            cache.set(user_id, history[-3:])
            return ClassifyResponse(success=True, data=question_response)

        if "database" in list(classify_result.keys()):
            database_type = classify_result["database"]
            chart_type = classify_result["chart_type"]

            if chart_type == "None" or chart_type == "null":
                chart_type = None

            history.append([question, json.dumps(classify_result)])
            cache.set(user_id, history[-3:])

            parts = database_type.split("_")
            if len(parts) < 2:
                raise ValueError(
                    "Invalid database_type format, expected 'dbCollection_responseType' format."
                )

            db_collection = parts[0]
            response_type = parts[1]

            logger.debug(f"Database type: {database_type}")

            return ClassifyResponse(
                success=True,
                data={
                    "dbCollection": db_collection,
                    "responseType": response_type,
                    "chart_type": chart_type,
                },
            )

        # Check for "SimplrOps" classification
        else:
            if classify_result["answer"].lower() == "simplrops":
                print("simplrops true")
                simplrops_data = simplrops_context(
                    simplrops_instructions, question, user_id, url
                )
                simplrops_data = correct_anchor_tags(simplrops_data)
                history.append([question, json.dumps(simplrops_data)])
                cache.set(user_id, history[-3:])
                return ClassifyResponse(success=True, data=simplrops_data)
            # Default case if neither pattern matches
            else:
                history.append([question, json.dumps(classify_result["answer"])])
                cache.set(user_id, history[-3:])
                return ClassifyResponse(success=True, data=classify_result["answer"])

    except ValueError as ve:
        logger.error(f"Value Error: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
