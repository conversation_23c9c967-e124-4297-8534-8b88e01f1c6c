import logging
import os
from urllib.parse import parse_qs, urlparse

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from fastapi import APIRouter
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from openai import AzureOpenAI

MODEL_NAME = "simplrops-gpt-4o"

# # Get environment variables from pod level
env_name = os.environ.get("ENVIRONMENT_NAME")
print(f"Environment name: {env_name}")
region_name = os.environ.get("AWS_REGION")
print(f"Region name: {region_name}")


def get_ssm_parameters(parameter_names):
    """
    Retrieves a list of parameters from the SSM store.

    Args:
        parameter_names (list): A list of parameter names to retrieve.

    Returns:
        dict: A dictionary where the keys are the parameter names and the values are the parameter values.

    Raises:
        ValueError: If any of the parameters are not found in the SSM store.
    """
    ssm_client = boto3.client("ssm", region_name=region_name)
    value = {}
    try:
        for i in parameter_names:
            para = ssm_client.get_parameter(
                Name=f"/{env_name}/{i}", WithDecryption=True
            )
            value[i] = para["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError) as e:
        print(f"Error fetching parameters: {str(e)}")
    except ssm_client.exceptions.ParameterNotFound as e:
        print(f"Parameter not found: {parameter_names}")
        raise ValueError(f"SSM Parameter '{parameter_names}' not found.")
    return value


def initialize_environment(parameter_names):
    """
    Initializes environment variables and returns an tuple containing:

    - client: an instance of the OpenAI client
    - router: an instance of the FastAPI router
    - model_name: the name of the OpenAI model
    - redis_password: the password for the Redis instance
    - redis_host: the hostname of the Redis instance

    Args:
        parameter_names (list): The list of parameter names to fetch from SSM.

    Returns:
        tuple: A tuple containing the initialized variables.
    """

    # Fetch parameters
    parameters = get_ssm_parameters(parameter_names)

    # Assign parameters to variables from ssm
    openai_api_key = parameters["openaikey"]
    redis_password = parameters["redispassword"]
    redis_host = parameters["redishost"]
    azure_endpoint = parameters["ai-model-endpoint"]
    # Assign parameters to variables from .env
    model_name, api_version = extract_api_version_and_model(azure_endpoint)
    print("\n--- Initialized Environment Variables ---")
    print(f"OpenAI API Key: {openai_api_key}")
    print(f"Redis Password: {redis_password}")
    print(f"Redis Host: {redis_host}")
    print(f"Azure Endpoint: {azure_endpoint}")
    print(f"Model Name: {model_name}")
    print(f"API Version: {api_version}")

    # Load environment variables
    client = AzureOpenAI(
        api_key=openai_api_key, api_version=api_version, azure_endpoint=azure_endpoint
    )
    router = APIRouter()

    return (
        client,
        router,
        model_name,
        redis_password,
        redis_host,
    )


def initialize_chain_environment(parameter_names):
    """
    Initializes a AzureChatOpenAI model with the given parameters.

    Args:
        parameter_names (list): The list of parameter names to fetch from SSM.

    Returns:
        AzureChatOpenAI: The initialized AzureChatOpenAI model.
    """

    # Fetch parameters
    parameters = get_ssm_parameters(parameter_names)

    # Assign parameters to variables from ssm
    openai_api_key = parameters["openaikey"]
    model_endpoint = parameters["ai-model-endpoint"]
    embeddings_endpoint = parameters["embedding_azure_endpoint"]
    # Assign parameters to variables from .env
    openai_model_name, openai_api_version = extract_api_version_and_model(
        model_endpoint
    )
    embedding_model_name, embedding_api_version = extract_api_version_and_model(
        embeddings_endpoint
    )

    model = AzureChatOpenAI(
        api_key=openai_api_key,
        temperature=0.4,
        model=openai_model_name,
        top_p=0.4,
        api_version=openai_api_version,
        azure_endpoint=model_endpoint,
    )
    embeddings = AzureOpenAIEmbeddings(
        api_key=openai_api_key,
        model=embedding_model_name,
        api_version=embedding_api_version,
        azure_endpoint=embeddings_endpoint,
    )
    return model, embeddings


def extract_api_version_and_model(url):
    parsed_url = urlparse(url)
    path_parts = parsed_url.path.strip("/").split("/")

    # Find model name (assuming it appears after "deployments/")
    model_name = None
    if "deployments" in path_parts:
        model_index = path_parts.index("deployments") + 1
        if model_index < len(path_parts):
            model_name = path_parts[model_index]

    # Extract API version from query parameters
    query_params = parse_qs(parsed_url.query)
    api_version = query_params.get("api-version", [None])[0]

    return model_name, api_version


def get_database_url():
    """
    Retrieves the database URL from the SSM parameters.

    Returns:
        str: The database URL.

    """
    try:
        local_db_url = os.environ.get("LOCAL_DB_URL")
        if local_db_url:
            print(f"Using LOCAL_DB_URL: {local_db_url}")
            return local_db_url
        else:
            parameters = get_mongo_ssm_parameters(["central/dburl"])
            print(f"Fetched DB URL: {parameters['central/dburl']}")
            return parameters["central/dburl"]
    except Exception as e:
        logging.error(f"Error retrieving database URL: {str(e)}")
        return None


def get_mongo_ssm_parameters(parameter_names):
    """
    Retrieves a list of parameters from the SSM store.

    Args:
        parameter_names (list): A list of parameter names to retrieve.

    Returns:
        dict: A dictionary where the keys are the parameter names and the values are the parameter values.

    Raises:
        ValueError: If any of the parameters are not found in the SSM store.

    """
    region_name = os.environ.get("AWS_REGION")
    ssm_client = boto3.client("ssm", region_name=region_name)
    value = {}
    try:
        for i in parameter_names:
            para = ssm_client.get_parameter(
                Name=f"/{env_name}/{i}", WithDecryption=True
            )

            value[i] = para["Parameter"]["Value"]
            print(
                f"Fetched MongoDB parameter: Name={para['Parameter']['Name']}, Value={value[i]}"
            )
    except (NoCredentialsError, PartialCredentialsError) as e:
        print(f"Error fetching parameters: {str(e)}")
    except ssm_client.exceptions.ParameterNotFound as e:
        print(f"Parameter not found: {parameter_names}")
        raise ValueError(f"SSM Parameter '{parameter_names}' not found.")
    return value
