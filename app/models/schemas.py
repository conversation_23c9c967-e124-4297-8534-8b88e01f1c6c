from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class ClassifyRequest(BaseModel):
    """Request model for classification"""

    prompt: str = Field(..., description="The prompt text to classify")

    class Config:
        json_schema_extra = {
            "example": {"prompt": "What is the process for user authentication?"}
        }


class GenerateQueryRequest(BaseModel):
    """Request model for query generation"""

    prompt: str = Field(..., description="The prompt text to generate query from")
    query_type: str = Field(..., description="Type of query to generate")
    chart_type: Optional[str] = Field(
        None, description="Type of chart for visualization"
    )
    dbSchema: Dict[str, Any] = Field(..., description="Database schema information")

    class Config:
        json_schema_extra = {
            "example": {
                "prompt": "Show me user logins by date",
                "query_type": "aggregation",
                "chart_type": "bar",
                "dbSchema": {
                    "collections": {"users": {"fields": ["username", "login_date"]}}
                },
            }
        }


class ClassifyResponse(BaseModel):
    """Response model for classification"""

    success: bool = Field(..., description="Whether the operation was successful")
    data: Any = Field(..., description="Classification result data")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "data": {"database": "users_list", "chart_type": "bar"},
            }
        }


class GenerateQueryResponse(BaseModel):
    """Response model for query generation"""

    success: bool = Field(..., description="Whether the operation was successful")
    data: str = Field(..., description="Generated MongoDB query")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "data": "db.users.aggregate([{$group: {_id: '$login_date', count: {$sum: 1}}}])",
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model"""

    success: bool = Field(
        default=False, description="Operation status (always false for errors)"
    )
    detail: str = Field(..., description="Error description")

    class Config:
        json_schema_extra = {
            "example": {"success": False, "detail": "Invalid query format"}
        }
