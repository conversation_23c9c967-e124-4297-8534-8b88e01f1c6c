from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>orizationCredentials, HTTPBearer
from jose import JWTError, jwt

from app.core.config import get_ssm_parameters

# Algorithm used for JWT verification
ALGORITHM = "RS256"


async def verify_jwt_token(token: str) -> dict:
    """
    Verify JWT token using public key from SSM.
    Returns the decoded payload if valid, raises HTTPException if not.
    """
    try:
        # Get public key from SSM
        params = get_ssm_parameters(["jwtPublicKey"])
        public_key = params["jwtPublicKey"]

        # Verify and decode the token
        payload = jwt.decode(token, public_key, algorithms=[ALGORITHM])
        return payload

    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve JWT public key",
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token verification failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


class JWTBearer(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(
            JWTBearer, self
        ).__call__(request)
        if credentials:
            if credentials.scheme != "Bearer":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid authentication scheme",
                )
            if not await verify_jwt_token(credentials.credentials):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid token or expired token",
                )
            return credentials.credentials
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid authorization code",
            )
