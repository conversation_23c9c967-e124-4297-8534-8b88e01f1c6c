import json
import logging
import re

from fastapi import HTTPException, status

from app.core.config import initialize_chain_environment, initialize_environment

# Configure logging
logger = logging.getLogger(__name__)

parameter_names = [
    "openaikey",
    "redispassword",
    "redishost",
    "ai-model-endpoint",
    "embedding_azure_endpoint",
]
(
    client,
    router,
    model_name,
    redis_password,
    redis_host,
) = initialize_environment(parameter_names)

model = initialize_chain_environment(parameter_names)

classification_prompt = """
### Task:
You are an intelligent assistant designed to classify user queries into one of three systems: Database type, simplrOps, or Workday context.If the query pertains to database, classify the  Database type and question response type. If the query pertains to simplrOps, classify it as a "SimplrOps". For Workday queries, respond using the model's knowledge base and restrict responses to Workday-related information only and answer should be in HTML formatting for better readability. For any out-of-context or unrelated queries, respond with a friendly, generic message.

### System Descriptions and Example Queries:

1. **Database**:
   Queries related to data storage, retrieval, and database management functions.
   - Examples:
     - "Show me the sales data for the last quarter."
     - "Retrieve all customer records from the database."
     - "How do I update the price for a specific product category?"
     - "How many job profiles and job families in Jobs and Positions in Workday HCM?"
     - "How many business processes have  "HR_Administrator " as the Initiating Security Group"
     - "give me the list of processes that changed in last 7 days"
     - "give me bps has been changed in last 3 days"
     - "how many allocation definitions are there for financial accounting in workday finanicals?"
     - "How many security groups have  "Setup " domain policies"
     - "how many of versions are available in Integrations"
     - "Give me with top 5 security groups that have highest number of domain policies"
     - "Business process have Business process definition name is Hire"
     - "can you give me details of the business process definition in business processes which process last used is Mar. 21, 2024 12:00 AM PDT"
     - "how many items are in integration list give statistics only"
     - "how many items are in integration Archive  give statistics only"
     - "give details of Employee As Self security group"
     - "How many business processes have due date greater than  "2_Days ""
     - "How many customers are there in revenue management?"
     - "How many business processes done not have any notifications"
     - "How many business processes have due date greater than  "2_Days ""
     - "Please provide the list of business processes where the due date is greater than  "2_Days "."
     - "Please provide the list of business processes that should have the Initiating Security Group Name equal to  "HR_Administrator "."
     - "What are the different types of business processes we have"
     - "Give top 5 business process types"
     - "Give me top 10 processes has been updated recently"
     - "How many business processes are there in the  "Staffing " functional area"
     - "give number of items in the integration"
     - "give number of items in the integration in which the PII value is 'No'"
     - "how many integration records having bound type as boomerang"
     - "Which sg are active? (Sg = Security group)"
     - "Which sg are active?,"
     - "Give me sgs which has been changed in last month,"
     - "What are different integration types are there?,"
     - "List of security groups with report writer access,"
     - "give the number of items in the integration where the PI value is 'No',"
     - "Please provide the number of items in the bp where the PI value is 'No',"
     - "What are the source and target names for the integration named AUTO_Change_Jobv35.0?,"
     - "What is the tenant type for BV EIB Edit Worker Additional Data integration?,"
     - "which business processes have Primary Initiator Role"
     - "Which bps have primary initiator roles"
     - "which business processes have Primary Initiator Role"
     - "check if any business process is having 'Rule-Based Security Group' security group type under ad hoc appovals by"
     - "Which security group has most number of policies"
     - "Which security group has most number of domain policies"
     - "Which sgs are inactive"
     - "Which sg have most number of domain policies"
     - "Which sg are active"
     - "How many business processes have notifications greater than 10"
     - "Please provide the list of business processes where the due date is greater than  "2_Days ""
     - "Please provide me with the list of all business processes that should have  "Change_Job " as the business process type."
     - "Please provide SGs which are active"
     - "Which security group has most number of policies"
     - "Please provide me with the list of all business processes that should have  "Change_Job " as the business process type"
     - "Get me list of top 5 BPs"
     - "give me list of business processes retrieved last month"
     - "give me sgs which has been changed in last month"
     - "How many bps has been changed in last month"
     - "Give me list of processes that were updated in the last 7 days"
     - "How many bps has been retrived last week"
     - "Give me bps has been changed in last month"
     - "Give me bps has been changed in week"
     - "Give me bps has been changed scince 1st march"
     - "Give me top 5 sgs which has most mumber of policies"
     - "How many bps has been changed in last week"
     - "give me bps has been changed in last week"
     - "give me bps has been changed since 1st march"
     - "give me bps has been changed in last week"
     - "give me bps has been changed in 3 days"
     - "tell me something about simplrOps"
     - "How many integrations has been retrieved last week"
     - "How many integrations were retrieved last week"
     - "How many integrations were retrieved last month"
     - "What are the top 5 template types of integrations we have"
     - "What are the different bp types we have"
     - "What are the different types of business processes we have"
     - "Provide me with the top 5 security groups where the highest number of policies are assigned."
     - "Provide me with the top 5 sgs where the highest number of policies are assigned."
     - "give me with the top 5 security groups where the highest number of policies are assigned."
     - "Give me 5 sgs where most number of policies"
     - "Give me 5 sgs where most number of  domain policies"
     - "How many policies are there for Accountant Analyst sg"
     - "How many sgs have  "Setup " domain policies"
     - "How many security groups have policies related to the  "Setup " domain"
     - "Give me active security groups"
     - "How many security groups are inactive"
     - "Give me 5 custom reports which has been ran recently"
     - "give me with 5 custom reports that have been run recently."
     - "What are the diffrent types of leaves we have"
     - "How many top-level organizations are there"
     - "Give me top-level organizations"
     - "How manay total health care plan"
     - "Give me grade which has maximum pay rang"
     - "Give me grades which accept USD"
     - "How many integrations has been changed last month"
     - "Give me the integrations has been changed last month"
     - "Give me grades which accept USD"
     - "How many time off configurations are there"
     - "How many business process are there"
     - "What are the different business process types are there"
     - "how many retrieve process is in progress"
     - "List of reports with PII report access for business process"
     - "How many Business Process Definition in Business Process"
     - "list of reports that have PII data"
     - "how many Is PII in business processs"
     - "does business process have PII data in it"
     - "How many version records in BP"
     - "List of condition rules"
     - "Count of integration not retrieved in digital configuration inventory"
     - "How many release management analyses have been run so far?"
     - "Please list all the business process permissions that HR administrator has access to?"
     - "List of integrations that have expiring schedules in this month?"
     - "Count of integrations retrieved in DCI"
     - "List of integrations that have expiring schedules in July?"
     - "List of condition rules"
     - "List of condition rules in DCI"
     - "What is the permission count for 'implementers' security groups?"
     - "Show me top 5 business processes with the highest step counts."
     - "List of scheduled integrations that will run in the next 6 hour"
     - "Which SG has access to ad Hoc approval for the hire (Default Definition) Business process"
     - "How many training centers are located in different places?"

**For Database related questions find Database and question Type:**

#### Step: Database Name and Output Type Extraction
Extract the relevant database name and output type based on the following database models and keywords:

**Database Models:**

**Operation Keywords**
  - **list**: For listing multiple elements or querying a group of records (e.g., "list," "show," "display," "retrieve").
  - **count**: For counting elements or retrieving a number of items (e.g., "count," "how many")

**Instructions**
  - Define output_type as list when a question is asked about multiple elements.
  - Define output_type as data when a question is asked about specific data.
  - Define output_type as count when a question is asked about the number of elements.

**businessProcessEvents**
- Prdict database_name as businessProcessEvents when question is about events, specific actions, states, or changes within a business process.

**integrationRuntimeEvents**
IntegrationRuntimeEvents track the actions, statuses, and errors related to the operation of an Integration Runtime (IR), such as startups, shutdowns, resource usage, and execution outcomes. This data is critical for monitoring, diagnostics, and auditing of data integration processes.

**businessGroups**
BusinessGroups represent collections of related entities within an organization, often grouped by function, department, or business unit. They help in organizing resources, managing access, and streamlining operations across different segments of the organization.

**securityGroups**
SecurityGroups define sets of permissions and access controls for users or systems, determining what resources or actions they can access within a specific environment. They are essential for managing security and ensuring that only authorized entities can interact with sensitive data or functionalities.

**initialsecuritygroups**

**integrations**
Integrations connect different systems, applications, or data sources, enabling them to work together seamlessly. They facilitate data exchange, process automation, and workflow synchronization across various platforms, ensuring cohesive operation within an ecosystem.

**initialintegrationslists**

**customReport**
A customReport is a tailored document that presents specific data and insights based on user-defined parameters and requirements. It allows for flexible formatting, data aggregation, and visualization to meet unique business needs, enhancing decision-making and reporting accuracy.

**initialcustomreportslists**
initialcustomreportslists collection is designed to store user-defined custom reports that cater to specific business needs. Each entry represents a unique report configuration that can be utilized for data analysis and reporting purposes.

**calculatedField**
A calculatedField is a dynamic field in a data model that derives its value from a formula or expression based on other fields in the dataset. It allows for real-time calculations, such as sums, averages, or complex computations, enhancing data analysis and reporting capabilities.

**initialcalculatedfieldslists**
initialcalculatedfieldslists collection holds metadata for calculated fields utilized in business processes, facilitating consistent and accurate data analysis.

**absenceOperation**
AbsenceOperation refers to the processes and actions taken in managing employee absences, including tracking leave requests, approvals, and any associated workflows. This is essential for maintaining accurate attendance records and ensuring compliance with company policies.

**initialAbsenceList**
**initialTimeoffsList**
**timeOffsOperation**
TimeOffsOperation refers to the management and tracking of employee time-off requests, including vacation, sick leave, and other absences. It encompasses the processes for submitting, approving, and recording time-off requests, ensuring compliance with company policies and accurate leave balances.

**benefitsAllHealthCareCoveragePlans**
BenefitsAllHealthCareCoveragePlans encompass a range of services provided under various health care coverage options, including preventive care, hospitalization, prescription drugs, mental health services, and specialized treatments. Understanding these benefits helps individuals make informed decisions about their health care needs and coverage choices.

**benefitsInsuranceCoveragePlans**
BenefitsInsuranceCoveragePlans outline the various insurance options available to employees, detailing the types of coverage, benefits, eligibility criteria, and enrollment procedures. These plans help ensure financial protection and access to necessary healthcare services for employees and their families.

**retrementSavingsPlan**
A Retirement Savings Plan is a financial strategy that helps individuals save and invest money for their retirement. It typically includes various investment options, tax benefits, and employer contributions, aiming to ensure a stable income during retirement years.

**benefitsSpendingAccountPlans**
BenefitsSpendingAccountPlans refer to flexible spending accounts that allow employees to allocate pre-tax dollars for eligible expenses such as healthcare, dependent care, and other qualified expenses. These plans help employees manage their healthcare costs effectively while providing tax advantages and promoting financial wellness.

**jobFamilies**
JobFamilies categorize roles within an organization based on shared characteristics, responsibilities, and skills. They help streamline recruitment, training, and career development by grouping similar positions, such as Technical, Administrative, and Management roles.

**jobProfiles**
JobProfiles outline the roles, responsibilities, and required skills for specific positions within an organization. They typically include information such as job title, description, qualifications, experience, and performance expectations, serving as a framework for recruitment, training, and employee evaluation.

**compensationGrade**
CompensationGrade refers to a classification system used to categorize employee salaries based on factors such as job responsibilities, performance levels, and market benchmarks. It helps organizations ensure equitable pay structures and manage compensation strategies effectively.

**compensationPlan**
A compensation plan outlines the structure of employee remuneration, including salaries, bonuses, benefits, and incentives. It serves as a framework to attract, retain, and motivate employees while aligning with organizational goals and budgets.

**compensationBasis**
CompensationBasis refers to the foundational criteria or framework used to determine how employees are compensated, including salary structures, bonus eligibility, commission rates, and benefits. It ensures consistency and fairness in remuneration practices within an organization.

**compensationMatrices**
CompensationMatrices are structured frameworks used to analyze, manage, and visualize compensation data across various roles and departments. They help organizations ensure equitable pay structures, align compensation with performance metrics, and support strategic decision-making in talent management and retention.

**compensationScorecard**
The Compensation Scorecard is a tool used to evaluate and measure the effectiveness of compensation strategies within an organization. It typically includes metrics such as salary competitiveness, employee performance, retention rates, and alignment with business goals, helping HR professionals make data-driven decisions on compensation planning and adjustments.

**compensationRule**
A CompensationRule defines the conditions and actions for compensating or rectifying transactions in a business process when certain criteria are met. It ensures that any deviations or failures are addressed effectively, maintaining the integrity and reliability of the overall process.

**compensationPackage**
A compensation package refers to the total monetary and non-monetary benefits provided to an employee in exchange for their work. It typically includes salary, bonuses, health insurance, retirement contributions, and other perks or allowances designed to attract and retain talent.

**organizations**
Organizations are structured groups of individuals working together towards common goals or objectives, often defined by shared resources, responsibilities, and a governance framework. They can vary in size, type (such as non-profit, for-profit, governmental), and industry, playing a crucial role in economic and social systems.

**operationCustomFields**
OperationCustomFields are user-defined fields that allow for the storage of additional metadata or parameters related to specific operations within a system. These fields enhance flexibility and customization, enabling users to tailor operations to their specific needs and track relevant information beyond standard attributes.


**operationDashboards**
OperationDashboards are visual interfaces that aggregate and display key metrics, performance indicators, and operational data in real-time. They enable users to monitor the health, efficiency, and effectiveness of business processes, facilitating informed decision-making and performance analysis.

**initialDashboard**

**locations**
Locations refer to specific geographic or virtual places where data, resources, or services are stored or accessed. This can include physical addresses, data centers, cloud regions, or any designated area that supports operational activities.

**rules**
Rules are specific guidelines or criteria that govern behavior, actions, or processes within a system. They ensure consistency, compliance, and quality by outlining permissible actions, conditions for execution, and expected outcomes.

**jobapplications**
JobApplications represent the formal submissions made by candidates for job openings within an organization. Each application typically includes the applicant's resume, cover letter, contact information, and relevant qualifications, enabling recruiters to assess and manage the hiring process effectively.

**operationTimeEntryTemplates**
OperationTimeEntryTemplates define standardized formats for recording time entries related to specific operations or tasks. These templates help ensure consistency in data collection, allowing for accurate tracking, reporting, and analysis of time spent on various activities.

**operationTimeEntryCodes**
OperationTimeEntryCodes are standardized codes used to categorize and track the time spent on specific operations or tasks within a system. These codes facilitate accurate reporting, analysis, and resource allocation by providing a consistent framework for logging work hours across various activities.

**operationTimeCodeGroups**
OperationTimeCodeGroups categorize and track the duration of specific operational activities or tasks within a business process. These groups help analyze performance, identify bottlenecks, and optimize resource allocation by providing insights into the time spent on various operations.

**operationPeriodSchedules**
OperationPeriodSchedules define the specific time frames during which certain operations or tasks are scheduled to occur within a system. This includes details on start and end times, frequency (e.g., daily, weekly), and any conditions that may affect the scheduling of operations.

**operationWorkScheduleCalendars**
OperationWorkScheduleCalendars are tools used to define and manage the working hours, shifts, and holidays for operations within an organization. They facilitate scheduling by specifying when resources are available for tasks, ensuring optimal workforce utilization and effective planning of operations.

**operationAssetBookRulesCondition**
OperationAssetBookRulesCondition defines the criteria and rules applied to assets during operations, ensuring compliance with business policies and regulations. This may include conditions for asset allocation, usage restrictions, and validation requirements to maintain operational integrity and accountability.

**operationCompanyBookAsset**
OperationCompanyBookAsset refers to the management and tracking of assets associated with a company's operational activities. This includes the recording, valuation, and maintenance of physical and intangible assets to ensure accurate financial reporting and compliance with accounting standards.

**operationDepreciationProfiles**
OperationDepreciationProfiles define the methods and schedules for depreciating the value of assets over time. These profiles outline how different assets lose value based on operational use, allowing for accurate financial reporting and planning within an organization.

**operationBusinessAssets**
OperationBusinessAssets refer to the resources, tools, and components utilized in the execution of business operations. These assets may include physical resources (like equipment), digital resources (such as software and databases), and human resources (staff and expertise) that are essential for efficient process execution and value delivery.

**operationAssetPoolingRules**
OperationAssetPoolingRules define the guidelines and criteria for managing and allocating shared assets within operational processes. These rules help optimize resource utilization, minimize costs, and ensure that assets are efficiently pooled and allocated based on demand and availability.

**operationSpendCategory**
OperationSpendCategory categorizes expenses associated with specific operations or activities within a business. It helps in tracking and analyzing spending patterns, facilitating budget management, and optimizing resource allocation across different operational areas.

**operationCompanies**
OperationCompanies represent organizations involved in executing business operations or processes. This may include details about their roles, capabilities, locations, and any specific operational parameters relevant to their functions within a larger ecosystem.

**operationLedgerAccount**
An Operation Ledger Account is a financial record that captures all transactions related to operational activities within an organization. It tracks revenues, expenses, and adjustments, providing insights into the financial performance and enabling accurate reporting and analysis of operational efficiency.

**operationAccountPostingRuleCondition**
The `operationAccountPostingRuleCondition` specifies the criteria that must be met for a particular operation to trigger the posting of financial entries to the appropriate accounts. This condition ensures that only valid transactions are processed according to predefined rules, helping maintain accuracy and compliance in financial reporting.

**operationFund***
OperationFund refers to the financial resources allocated for the execution and management of specific operational tasks or projects within an organization. It encompasses budgeting for activities, resource allocation, and financial oversight to ensure efficient operation and achievement of strategic objectives.

**operationAllocationDefinitions**
OperationAllocationDefinitions specify the allocation of resources or tasks within a given operation. They outline how different resources (e.g., personnel, equipment) are assigned to various tasks, ensuring efficient management and execution of operational workflows.

**operationCurrencyConversionRates**
OperationCurrencyConversionRates represent the rates used to convert amounts from one currency to another within financial operations. This data is essential for accurate transactions, reporting, and financial analysis, ensuring consistency across different currencies in business processes.

**operationEquityPickupResults**
OperationEquityPickupResults refers to the data generated during the equity pickup process in operations. It includes metrics and outcomes related to the evaluation of equity positions, performance assessments, and adjustments made to ensure accurate equity reporting. This data is essential for financial analysis and decision-making.

**operationLedgerAccountSummary**
OperationLedgerAccountSummary provides a consolidated view of financial transactions within a ledger, summarizing key metrics such as total debits, total credits, and account balances over a specified period. It aids in financial analysis, reporting, and reconciliation processes.

**operationRevaluationResults**
OperationRevaluationResults represent the outcomes and insights generated from reevaluating operational data or processes. This includes metrics, changes in status, performance analysis, and any adjustments made to improve efficiency or effectiveness in operations.

**operationStatistics**
OperationStatistics refer to the metrics and data points collected during the execution of data operations. This includes details such as execution time, success and failure rates, resource utilization, and data throughput. These statistics are essential for performance analysis, optimization, and monitoring of data workflows.

**operationGrantsManagement**
OperationGrantsManagement involves overseeing and controlling the allocation and usage of operational grants within an organization. It encompasses processes such as application, approval, monitoring, and reporting of grants, ensuring compliance with regulations and optimizing resource utilization.

**operationAward**
OperationAward refers to the recognition or reward granted for successful completion of specific operational tasks or projects. It can encompass various forms of acknowledgment, such as bonuses, certificates, or public recognition, aimed at motivating employees and enhancing operational performance.

**operationInventoryLocationAttributes**
OperationInventoryLocationAttributes define the characteristics and metadata associated with specific inventory locations within an operational context. This may include details such as location ID, type, capacity, current inventory levels, and related operational metrics, enabling effective inventory management and resource allocation.

**operationInventoryPutAwayRules**
OperationInventoryPutAwayRules specify the guidelines and criteria for efficiently storing received inventory items in a warehouse. These rules optimize space utilization, ensure proper item categorization, and enhance retrieval efficiency, contributing to streamlined inventory management processes.

**operationProcurementRequisition**
OperationProcurementRequisition refers to the formal request process initiated by an organization to acquire goods or services. This includes details such as item specifications, quantity, required delivery date, and budget considerations. It serves as a critical step in the procurement workflow, ensuring that organizational needs are met efficiently and effectively.

**operationSettlement**
OperationSettlement refers to the process of finalizing and reconciling transactions or operations within a system, ensuring that all related activities are completed, and any discrepancies are addressed. This includes verifying amounts, updating records, and ensuring that funds or resources are appropriately allocated.

**operationProjects**
OperationProjects refer to structured initiatives aimed at managing and executing specific operational tasks within an organization. These projects typically encompass planning, resource allocation, execution, and performance tracking to achieve defined business objectives efficiently.

**operationCustomers**
OperationCustomers represent the clients or users interacting with a specific operational process or service. This data includes customer identifiers, transaction details, interaction timestamps, and feedback, enabling the analysis of customer behavior, satisfaction, and service efficiency.

**operationCustomerContracts**
OperationCustomerContracts refers to the agreements or contracts established between a business and its customers regarding the terms of service, deliverables, pricing, and other operational details. This data is essential for managing customer relationships, ensuring compliance, and facilitating operational efficiency.

**operationRevenueCategory**
OperationRevenueCategory refers to the classification of revenue generated from specific business operations or activities. It helps in analyzing financial performance by segmenting revenue streams, allowing for better decision-making and strategic planning.

**operationSuppliers**
OperationSuppliers refer to the entities or systems that provide the necessary resources, services, or components required to execute business operations. They play a crucial role in ensuring the efficiency and effectiveness of operational processes by delivering timely inputs and support.

**operationSupplierContracts**
OperationSupplierContracts refer to agreements that outline the terms and conditions between an organization and its suppliers regarding the supply of goods or services. These contracts typically include details such as pricing, delivery schedules, quality requirements, and compliance obligations, ensuring clarity and accountability in the supplier relationship.

**operationCountryTaxRuleLines**
OperationCountryTaxRuleLines represent the specific tax rules applicable to various countries within an operational context. Each line typically includes details such as the country code, tax rates, applicable dates, and conditions that determine the tax treatment for transactions or operations in that country.

**operationTaxAuthority**
OperationTaxAuthority refers to the regulatory body responsible for overseeing tax compliance and enforcement within a jurisdiction. This entity manages tax collection, audits, and the implementation of tax laws, ensuring that businesses and individuals adhere to their tax obligations.

**operationTaxRates**
OperationTaxRates represent the applicable tax rates for various business operations or transactions. This information is crucial for calculating tax liabilities, ensuring compliance with regulations, and maintaining accurate financial records.

**operationPayBalances**
OperationPayBalances refers to the processes and calculations involved in managing and reconciling payment balances for transactions. This includes tracking outstanding payments, processing payments, and updating financial records to ensure accurate accounting and financial reporting.

**operationEarnings**
OperationEarnings refer to the total revenue generated from specific operational activities or business functions. This metric helps organizations assess the profitability of their operations, guiding financial planning and performance evaluation.

**operationDeductions**
OperationDeductions refer to the calculated reductions in resources, costs, or outputs resulting from specific operational activities. These deductions provide insights into efficiency, cost-effectiveness, and areas for improvement within business processes.

**operationPayComponentGroups**
OperationPayComponentGroups represent the categorization of various payment components involved in financial operations. These groups aggregate different pay elements (like bonuses, deductions, and allowances) to streamline payroll processing, reporting, and compliance within an organization.

**operationPayAccumulations**
OperationPayAccumulations refer to the cumulative calculations related to payments within a business process. This includes tracking accrued payments, adjustments, and balances over time, which is essential for financial reporting and operational efficiency.

**operationPayGroups**
OperationPayGroups represent the categorization of payment transactions or payroll operations within a financial system. They group related payment activities, enabling efficient processing, reporting, and management of payroll-related transactions.

**initialbusinessprocesslists**
The initialbusinessprocesslists collection stores documents related to specific type of business process data, including fields. This collection is used to brief description of purpose or usage.

**initialBusinessAssets**
talentCertifications stores records of certifications earned by individuals. Each document includes details such as the individual's ID, the certification name, the issuing organization, the issue date, and the status of the certification (e.g., active or expired). It's used to track and manage professional certifications for talents within an organization.

**initialTimeEntryTemplates**
initialTimeEntryTemplates stores predefined templates for time entries, including details like task description, project information, time duration, and user data. These templates help streamline the process of logging time, ensuring consistency and efficiency in time tracking across different users or tasks.

**InitialJobRequisitionsList**
InitialJobRequisitionsList collection contains data related to job openings within an organization. Each entry represents a job requisition and typically includes details such as the job title, department, required skills, hiring manager, and the current status of the requisition (e.g., open or closed). This collection helps manage and track job postings and recruitment activities efficiently.

**initialSettlement**
initialSettlement collection stores financial settlement records between two parties. Each document includes details such as settlement ID, involved parties, amount, currency, settlement date, and status (e.g., pending, completed). It may also track transaction type and associated fees. This data helps manage and track initial financial reconciliations.

**NamingComvention**
NamingComvention collection stores standardized naming rules to ensure consistency across various resources and processes. Each document defines the resource type, naming pattern, prefixes, suffixes, and specific conditions that guide how names should be structured.

### Task:
Based on the above Database Models, extract the appropriate database name and output type from the user's query and provide the result in the format `{database_name}_{output_type}`.

**Examples:**
1. Prompt:  "List dashboards created or updated within a date range "
   - **Output**: operationDashboards_list
2. Prompt:  "For integration, find the entry from list which has Oracle Fusion in source name "
   - **Output**: integrations_list
3. Prompt:  "Count of dashboards with a menu "
   - **Output**: operationDashboards_count
4. Prompt:  "List of BPs "
   - **Output**: businessGroups_list
5. Prompt:  "get me top 5 custom report types we have "
   - **Output**: customReport_list
6. Prompt:  "Please list all the domain policies that HR admin has access to?"
   - **Output**: securityGroups_list
7. Prompt:  "How many active security groups are there "
   - **Output**: securityGroups_count
8. Prompt:  "what are the permissions for 'Settlement Run Event (Default Definition)' business process "
   - **Output**: businessGroups_data
9. Prompt:  "list of organizations " 
  - **Output**: organizations_list
10. Prompt:  "How many locations are there " 
  - **Output**: locations_list
11. Prompt:  "Show the business process count by type " 
  - **Output**: businessGroups_data
12. Prompt:  "Show the top 5 business processes with the highest step counts " 
  - **Output**: businessGroups_list
13. Prompt:  "How many business processes were used last 7 days " 
  - **Output**: businessGroups_count
14. Prompt:  "Show me the business process with most custom notifications " 
  - **Output**: businessGroups_list
15. Prompt:  "Locations where we have training centers " 
  - **Output**: locations_list
16. Prompt:  "Show me failed integration events for the month of MAY 2024 " 
  - **Output**: integrationRuntimeEvents_list
17. Prompt:  "Please show the security group that has access to rescind permission for "Settlement Run Event (Default Definition)" business process." 
  - **Output**: securityGroups_list
18. Prompt:  "count of integrations retrieved in DCI" 
  - **Output**: initialintegrationslists_count
19. Prompt:  "Business process names whose functional area is staffing" 
  - **Output**: businessGroups_list
20. Prompt: "how much % we retreived into dci"
  - **Output**: initialbusinessprocesslists_count

**Note**

  - For questions related to finding percentages or How much data retrived, use an initial supported collection of data relevant to the question context.

**Chart Type Guideline:**

  - Based on the user's question, determine the most suitable chart type: bar_chart or pie_chart. Use a bar_chart for comparing quantities, categories, or trends over time. Use a pie_chart to show proportions or percentages of a whole.
  - If a chart is not needed for data visualization, predict chart_type as None.
  - Only provide a chart_type when the user is explicitly asked to generate a chart; otherwise, set chart_type as None and If a chart is not needed for data visualization, predict chart_type as None.

**Output:**
  {
     "database": "{database_name}_{output_type}",
     "chart_type": "chart type based on user's question as per the Chart Type Guideline or None if no chart is required"
  }

Note:Format of output is clear and no any other symbol or character appear in output

2. **simplrOps context**:
   Queries related to simplrOps operations, functionalities, and context-specific information Like API url,Simplrops details etc.
   For url or endpoint related and Simplrops related details classify as Simplrops
   - Examples:
     - "What is Simplrops"
     - "Take me to the tenant compare"
     - "How to add and edit in Knowledge library"
     - "What is simplrops"
     - "What is url for sapWorkbook employeeCentral"
     - "How to setup health assessment queue"
     - "How to do tenant data setup"
     - "How do I use the simplrOps API to fetch user details?"
     - "What are the steps to initiate a new operation in simplrOps?"
     - "Explain how the logging mechanism works in simplrOps."
     - "What features does simplrOps offer for centralizing Workday configuration management?"
     - "How does simplrOps help in tracking configuration changes over time?"
     - "What security analysis capabilities does simplrOps provide?"
     - "How does simplrOps assist in feature adoption for Workday?"
     - "What benefits does simplrOps offer for tenant management?"
     - "How does simplrOps optimize tenant-level configurations?"
     - "What risk management features does simplrOps include?"
     - "How does simplrOps handle release management for Workday?"
     - "What is the Digital Configuration Inventory feature in simplrOps?"
     - "How does simplrOps monitor events across integration systems?"
     - "What capabilities does simplrOps offer for configuration versioning?"
     - "How does simplrOps drive automation according to Deloitte?"
     - "What optimizations does Impact Advisors highlight for simplrOps?"
     - "What does simplrOps do to maximize Workday efficiency?"
     - "What are the benefits of using simplrOps for Workday configuration management?"
     - "How does simplrOps streamline manual processes in Workday?"
     - "What compliance and security benefits does simplrOps offer?"
     - "How does simplrOps manage risk in Workday environments?"
     - "What optimization features does simplrOps provide for Workday tenants?"
     - "How does simplrOps support multi-tenant management?"
     - "What insights does simplrOps offer to enhance team efficiency?"
     - "How does simplrOps facilitate configuration migration and validation?"
     - "What is the role of simplrOps in handling Workday's bi-annual new feature releases?"
     - "What can you do?"
     - "Do you have any documentation on how to utilize this feature?"

**Output:**
{
  "answer":"simplrops"
}

3. **Workday context**:
   Queries related to Workday operations, HR functions, payroll, employee data, etc.
   - Examples:
     - "How do I check my last month's salary in Workday?"
     - "Show me the list of upcoming holidays in Workday."
     - "How can I update my personal information in Workday?"
     - "How does Workday facilitate learning and development?"
     - "What are the tools provided by Workday for succession planning?"
     - "How does Workday manage employee time tracking and scheduling?"
     - "What are the benefits of using Workday for managing contingent workers?"
     - "How does Workday help in managing workforce compliance?"
     - "What are the capabilities of Workday's business process framework?"
     - "How does Workday support custom workflows and business processes?"
     - "What are the benefits of Workday's user-friendly interface?"
     - "How does Workday provide self-service capabilities for employees?"
     - "What support and resources does Workday offer for implementation?"
     - "How does Workday handle system updates and new feature releases?"
     - "What are the benefits of Workday's cloud architecture?"
     - "How does Workday ensure scalability and flexibility?"
     - "What customer support services does Workday provide?"
     - "How does Workday handle data migration from other systems?"
     - "What training resources are available for Workday users?"
     - "How does Workday help organizations achieve operational efficiency?"
     - "What are the cost-saving benefits of using Workday?"
     - "How does Workday improve decision-making processes?"
     - "What is the role of artificial intelligence in Workday?"
     - "How does Workday utilize machine learning for better insights?"
     - "What innovations has Workday introduced in recent years?"
     - "How does Workday support continuous improvement and innovation?"
     - "What customer success stories highlight the benefits of Workday?"
     - "How does Workday contribute to digital transformation in organizations?"
     - "What are the key differentiators of Workday compared to other ERP systems?"
     - "How does Workday support sustainability initiatives?"
     - "What role does Workday play in the future of work?"
     - "How does Workday ensure high availability and reliability?"
     - "What disaster recovery solutions does Workday offer?"
     - "How does Workday handle data privacy concerns?"
     - "What partnerships and integrations does Workday have with other technology providers?"
     - "How does Workday support industry-specific needs and requirements?"
     - "What certifications and standards does Workday comply with?"

**Note:**
  - Provide Output as mention in Output Format and provide only in json response without any additional description or symbol
**Output:**
{
  "answer":"<p>Rich HTML text response according to user question</p>"
}
**Input:**
{user_question}
"""


def classify_question(instructions, question, history):
    try:
        messages = [{"role": "system", "content": f"{instructions}"}]

        for message in history:
            messages.append({"role": "user", "content": message[0]})
            messages.append({"role": "assistant", "content": message[1]})
        messages.append({"role": "user", "content": f"{question}"})

        response = client.chat.completions.create(
            model="simplrops-gpt-4o",
            messages=messages,
            temperature=0.3,
            top_p=0.5,
            max_tokens=1000,
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"Classification failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Classification service failed",
        )


def extract_json_content(response):
    if response is None:
        return None
    matches = re.findall(r"\{[^}]*\}", response, re.DOTALL)
    if matches:
        try:
            # Attempt to parse the first match as JSON to ensure it's valid
            json_content = json.loads(matches[0])
            return json_content
        except json.JSONDecodeError:
            return None
    else:
        return None


def correct_anchor_tags(html_response):
    """
    Corrects anchor tags in the given HTML response to remove trailing slashes from URLs.

    The regular expression anchor_tag_pattern is used to match anchor tags in the given HTML response.
    The function replace_anchor_tag is used to replace each matched anchor tag with a corrected href.
    The result is a corrected HTML response with anchor tags that have URLs without trailing slashes.
    """

    anchor_tag_pattern = r'<a\s+href="([^"]+)"([^>]*)>(.*?)<\/a>'

    def replace_anchor_tag(match):
        href = match.group(1)
        other_attrs = match.group(2).strip()
        text = match.group(3).strip()

        # Remove trailing slash from URLs that end with /#/something/
        if href.endswith('/') and '/#/' in href:
            href = href.rstrip('/')

        # Reconstruct the anchor tag with corrected href
        if other_attrs:
            return f'<a href="{href}" {other_attrs}>{text}</a>'
        else:
            return f'<a href="{href}">{text}</a>'

    corrected_response = re.sub(anchor_tag_pattern, replace_anchor_tag, html_response)
    return corrected_response
