import logging

import boto3

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_ssm_parameters(parameter_names, env_name="dev1", region_name="ap-south-1"):
    """Retrieve parameters from SSM store."""
    ssm_client = boto3.client("ssm", region_name=region_name)
    values = {}

    try:
        for param_name in parameter_names:
            full_param_name = f"/{env_name}/{param_name}"
            response = ssm_client.get_parameter(
                Name=full_param_name, WithDecryption=True
            )
            values[param_name] = response["Parameter"]["Value"]
            logger.info(f"Successfully retrieved parameter: {param_name}")
    except Exception as e:
        logger.error(f"Error fetching SSM parameters: {e}")
        raise

    return values


def get_mongo_ssm_parameters(
    parameter_names, env_name="dev1", region_name="ap-south-1"
):
    """Retrieve MongoDB parameters from SSM store."""
    ssm_client = boto3.client("ssm", region_name=region_name)
    values = {}

    try:
        for param_name in parameter_names:
            full_param_name = f"/{env_name}/{param_name}"
            response = ssm_client.get_parameter(
                Name=full_param_name, WithDecryption=True
            )
            values[param_name] = response["Parameter"]["Value"]
            logger.info(f"Successfully retrieved parameter: {param_name}")
    except Exception as e:
        logger.error(f"Error fetching MongoDB SSM parameters: {e}")
        raise

    return values


def main():
    # Get standard parameters
    params = get_ssm_parameters(
        ["openaikey", "embedding_azure_endpoint", "ai-model-endpoint", "central/dburl"]
    )

    # Print all values
    print("\nSSM Parameter Values:")
    print("-" * 50)
    print(f"OpenAI Key: {params.get('openaikey', 'Not found')}")
    print(
        f"Embedding Azure Endpoint: {params.get('embedding_azure_endpoint', 'Not found')}"
    )
    print(f"AI Model Endpoint: {params.get('ai-model-endpoint', 'Not found')}")
    print(f"MongoDB URL: {params.get('central/dburl', 'Not found')}")


if __name__ == "__main__":
    main()
