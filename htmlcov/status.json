{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.9", "globals": "368afc63fb8bcf9796a618221aeea649", "files": {"z_801e9b074b0e6333_classifier_py": {"hash": "f6ef56c643e795e2fa2ea11a5d82b76c", "index": {"url": "z_801e9b074b0e6333_classifier_py.html", "file": "app/api/v1/controllers/ollie/classifier.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_801e9b074b0e6333_query_py": {"hash": "1c58beeed6fa1152c39d8ce07f6fa1f4", "index": {"url": "z_801e9b074b0e6333_query_py.html", "file": "app/api/v1/controllers/ollie/query.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_257b53c25398f6ee_router_py": {"hash": "975c66faf8753c97da002e9a291e9672", "index": {"url": "z_257b53c25398f6ee_router_py.html", "file": "app/api/v1/router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_config_py": {"hash": "5e509790562a8c09ee868bbb2b3a2373", "index": {"url": "z_8f7e1016f2d37417_config_py.html", "file": "app/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33d94c5b4d36d1aa_database_py": {"hash": "a93fdcd313a422e12e703eea7e8ab75a", "index": {"url": "z_33d94c5b4d36d1aa_database_py.html", "file": "app/database/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_schemas_py": {"hash": "f6fa0aea0ee9e9d02e6488ca95df4d30", "index": {"url": "z_6c0e4b930745278b_schemas_py.html", "file": "app/models/schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_server_py": {"hash": "c3b9836e2a6e1a253eef73f65e26df21", "index": {"url": "z_5f5a17c013354698_server_py.html", "file": "app/server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_auth_service_py": {"hash": "f7448dd1ec45518b9fd61768c2fcd235", "index": {"url": "z_c318f3fa19a49f69_auth_service_py.html", "file": "app/services/auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84b4a12393a39fbf_classifier_service_py": {"hash": "0a5af27319238e6b685fcfd27f00f8e6", "index": {"url": "z_84b4a12393a39fbf_classifier_service_py.html", "file": "app/services/ollie_service/classifier_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84b4a12393a39fbf_query_service_py": {"hash": "18cad37f0e7d26b961c62ef47f799c4d", "index": {"url": "z_84b4a12393a39fbf_query_service_py.html", "file": "app/services/ollie_service/query_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84b4a12393a39fbf_simplrops_context_service_chroma_py": {"hash": "d1312bbb98b8c45217bd1a449fd76d3f", "index": {"url": "z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html", "file": "app/services/ollie_service/simplrops_context_service_chroma.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84b4a12393a39fbf_simplrops_context_service_mongo_py": {"hash": "4a4dd6058d25d34f35afd15b0cb9b158", "index": {"url": "z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html", "file": "app/services/ollie_service/simplrops_context_service_mongo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7617613e321ff6e7_get_instructions_py": {"hash": "e2a2bb1749061a0283c4d7d87a48d181", "index": {"url": "z_7617613e321ff6e7_get_instructions_py.html", "file": "app/services/utils/get_instructions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7617613e321ff6e7_redis_setup_py": {"hash": "ace42482c51c638584838e939ffaea9e", "index": {"url": "z_7617613e321ff6e7_redis_setup_py.html", "file": "app/services/utils/redis_setup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7617613e321ff6e7_remove_markdown_py": {"hash": "98529c56824c012bdc038fa092ffb4bc", "index": {"url": "z_7617613e321ff6e7_remove_markdown_py.html", "file": "app/services/utils/remove_markdown.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}