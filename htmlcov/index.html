<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">83%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_classifier_py.html">app/api/v1/controllers/ollie/classifier.py</a></td>
                <td>68</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="61 68">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_query_py.html">app/api/v1/controllers/ollie/query.py</a></td>
                <td>48</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="44 48">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_router_py.html">app/api/v1/router.py</a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td>91</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="85 91">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html">app/database/database.py</a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_schemas_py.html">app/models/schemas.py</a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html">app/server.py</a></td>
                <td>35</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="34 35">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html">app/services/auth_service.py</a></td>
                <td>29</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="15 29">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html">app/services/ollie_service/classifier_service.py</a></td>
                <td>46</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="45 46">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html">app/services/ollie_service/query_service.py</a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html">app/services/ollie_service/simplrops_context_service_chroma.py</a></td>
                <td>82</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="35 82">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html">app/services/ollie_service/simplrops_context_service_mongo.py</a></td>
                <td>87</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="69 87">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_get_instructions_py.html">app/services/utils/get_instructions.py</a></td>
                <td>21</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="18 21">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html">app/services/utils/redis_setup.py</a></td>
                <td>25</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="23 25">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_remove_markdown_py.html">app/services/utils/remove_markdown.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>624</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="520 624">83%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_7617613e321ff6e7_remove_markdown_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_801e9b074b0e6333_classifier_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
