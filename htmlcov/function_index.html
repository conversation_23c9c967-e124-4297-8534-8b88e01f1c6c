<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">83%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_classifier_py.html#t48">app/api/v1/controllers/ollie/classifier.py</a></td>
                <td class="name left"><a href="z_801e9b074b0e6333_classifier_py.html#t48"><data value='query_classification'>query_classification</data></a></td>
                <td>51</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="44 51">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_classifier_py.html">app/api/v1/controllers/ollie/classifier.py</a></td>
                <td class="name left"><a href="z_801e9b074b0e6333_classifier_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_query_py.html#t47">app/api/v1/controllers/ollie/query.py</a></td>
                <td class="name left"><a href="z_801e9b074b0e6333_query_py.html#t47"><data value='generate_query'>generate_query</data></a></td>
                <td>33</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="29 33">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_801e9b074b0e6333_query_py.html">app/api/v1/controllers/ollie/query.py</a></td>
                <td class="name left"><a href="z_801e9b074b0e6333_query_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_router_py.html#t34">app/api/v1/router.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee_router_py.html#t34"><data value='protected_route'>protected_route</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_router_py.html">app/api/v1/router.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee_router_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t20">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t20"><data value='get_ssm_parameters'>get_ssm_parameters</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t49">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t49"><data value='initialize_environment'>initialize_environment</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t99">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t99"><data value='initialize_chain_environment'>initialize_chain_environment</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t142">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t142"><data value='extract_api_version_and_model'>extract_api_version_and_model</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t160">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t160"><data value='get_database_url'>get_database_url</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t182">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t182"><data value='get_mongo_ssm_parameters'>get_mongo_ssm_parameters</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t20">app/database/database.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t20"><data value='connect_to_mongo'>connect_to_mongo</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t43">app/database/database.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t43"><data value='close_mongo_connection'>close_mongo_connection</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t54">app/database/database.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html#t54"><data value='get_database'>get_database</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html">app/database/database.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_schemas_py.html">app/models/schemas.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t38">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t38"><data value='read_root'>read_root</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t45">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t45"><data value='startup_db_client'>startup_db_client</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t51">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t51"><data value='shutdown_db_client'>shutdown_db_client</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t63">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t63"><data value='http_exception_handler'>http_exception_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t70">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html#t70"><data value='general_exception_handler'>general_exception_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html">app/server.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="27 28">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t11">app/services/auth_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t11"><data value='verify_jwt_token'>verify_jwt_token</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t45">app/services/auth_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t45"><data value='init__'>JWTBearer.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t48">app/services/auth_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html#t48"><data value='call__'>JWTBearer.__call__</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html">app/services/auth_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t578">app/services/ollie_service/classifier_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t578"><data value='classify_question'>classify_question</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t603">app/services/ollie_service/classifier_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t603"><data value='extract_json_content'>extract_json_content</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t618">app/services/ollie_service/classifier_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t618"><data value='correct_anchor_tags'>correct_anchor_tags</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t629">app/services/ollie_service/classifier_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html#t629"><data value='replace_anchor_tag'>correct_anchor_tags.replace_anchor_tag</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html">app/services/ollie_service/classifier_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_classifier_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html#t258">app/services/ollie_service/query_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html#t258"><data value='preprocess_query'>preprocess_query</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html#t268">app/services/ollie_service/query_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html#t268"><data value='get_query'>get_query</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html">app/services/ollie_service/query_service.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_query_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html#t165">app/services/ollie_service/simplrops_context_service_chroma.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html#t165"><data value='simplrops_context'>simplrops_context</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html">app/services/ollie_service/simplrops_context_service_chroma.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html#t174">app/services/ollie_service/simplrops_context_service_mongo.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html#t174"><data value='simplrops_context'>simplrops_context</data></a></td>
                <td>47</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="30 47">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html">app/services/ollie_service/simplrops_context_service_mongo.py</a></td>
                <td class="name left"><a href="z_84b4a12393a39fbf_simplrops_context_service_mongo_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="39 40">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_get_instructions_py.html#t9">app/services/utils/get_instructions.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_get_instructions_py.html#t9"><data value='get_instructions'>get_instructions</data></a></td>
                <td>16</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="13 16">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_get_instructions_py.html">app/services/utils/get_instructions.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_get_instructions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t34">app/services/utils/redis_setup.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t34"><data value='get'>get</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t47">app/services/utils/redis_setup.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t47"><data value='set'>set</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t52">app/services/utils/redis_setup.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html#t52"><data value='delete'>delete</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html">app/services/utils/redis_setup.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_redis_setup_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_remove_markdown_py.html#t1">app/services/utils/remove_markdown.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_remove_markdown_py.html#t1"><data value='remove_markdown'>remove_markdown</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7617613e321ff6e7_remove_markdown_py.html">app/services/utils/remove_markdown.py</a></td>
                <td class="name left"><a href="z_7617613e321ff6e7_remove_markdown_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>624</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="520 624">83%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
