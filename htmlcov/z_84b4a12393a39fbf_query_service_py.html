<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for app/services/ollie_service/query_service.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>app/services/ollie_service/query_service.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">16 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">16<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_84b4a12393a39fbf_classifier_service_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">from</span> <span class="nam">app</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">config</span> <span class="key">import</span> <span class="nam">initialize_chain_environment</span><span class="op">,</span> <span class="nam">initialize_environment</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="nam">parameter_names</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">    <span class="str">"openaikey"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">    <span class="str">"redispassword"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">    <span class="str">"redishost"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">    <span class="str">"ai-model-endpoint"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">    <span class="str">"embedding_azure_endpoint"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">    <span class="nam">client</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">    <span class="nam">router</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="nam">model_name</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">    <span class="nam">redis_password</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="nam">redis_host</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="op">)</span> <span class="op">=</span> <span class="nam">initialize_environment</span><span class="op">(</span><span class="nam">parameter_names</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">model</span> <span class="op">=</span> <span class="nam">initialize_chain_environment</span><span class="op">(</span><span class="nam">parameter_names</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">query_prompt</span> <span class="op">=</span> <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="str">Create MongoDB aggregate queries based on user requests, adhering to the following guidelines:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="str">### step: MongoDB Aggregate Query Guidelines</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="str">1. **Stages Order**: Always use stages in the order: `$group`, `$match`, `$project`.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="str">    - Enclose each stage in curly braces .</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="str">    - Use a JSON array `[]` to encapsulate the full query.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="str">2. **Filtering and Ordering**: </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="str">    - Use `$match` for filtering documents.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="str">    - Use `$sort` for ordering results, especially when counting occurrences.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="str">    - Apply descending order for count values by default.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="str">3. **Field Inclusion**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="str">    - Use `$project` to include specific fields in the output.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="str">    - Always include `simplropsId` and `NAMEFIELD` when listing items, unless a specific field value is requested.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="str">    - Always include the `_id` for Integration Event Detail or Integration Runtime Events models only. For all other queries, remove `_id` from the response by including {_id:0} into the $project query.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="str">    - Always include following condition "{ \"$match\": { \"integration\": { \"$exists\": True } } }" and "{ \"$lookup\": { \"from\": \"integrations\", \"localField\": \"integration\", \"foreignField\": \"_id\", \"as\": \"integration\" } }, { \"$unwind\": { \"path\": \"$integration\", \"preserveNullAndEmptyArrays\": False } }" in a proper order to ensure quick response from database for  Integration Event Detail or integrationRuntimeEvents models</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="str">    - Replace the integration name with `NAMEFIELD`, group the integrations after unwinding the query, set the first document as the root to remove duplicates, and add a field called `link` with the value 'DCI' when the user requests the status of a specific integration rather than events like a list of failed or completed integrations. The user can also inquire about the status of the same specific integration within a certain timeframe. </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="str">4. **Field and Array Operations**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="str">    - For counting array elements, use the `$size` operator.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="str">    - Use `$elemMatch` to specify conditions within array elements.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t"><span class="str">    - Ensure `$size` expects and accepts only numerical values.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="str">5. **Regex and Case Sensitivity**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t"><span class="str">    - Use `$regex` with the `i` option for case-insensitive searches.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t"><span class="str">6. **Dates and Boolean Values**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t"><span class="str">    - Convert given dates to UTC 0 (e.g., `Mar. 21, 2024 10:47 PM PDT` to `2024-03-22T05:47:00.000+00:00`).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="str">    - Use `"False"` for `No` and `"True"` for `Yes` in boolean fields.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t"><span class="str">    - For date ranges like "last week" or "last month", dynamically calculate the date range from the current date.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t"><span class="str">7. **Changed/Updated Items**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t"><span class="str">    - Include a check for `versionCount > 0` when querying for changed or updated items.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t"><span class="str">    - For `businessGroups`, use `definitionLastUpdated` as the default field and `lastFunctionallyUpdated` as the default field for item updates.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t"><span class="str">8. **Specific Mappings**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t"><span class="str">    - Map specific prompts to fields:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t"><span class="str">        - "integrations from" &#8594; `sourceName`</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t"><span class="str">        - "integrations to" &#8594; `targetName`</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t"><span class="str">        - "template type" &#8594; `technologyType`</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t"><span class="str">9. **Strict Formatting**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t"><span class="str">    - Ensure the output strictly conforms to JSON.stringify format for MongoDB aggregate queries.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t"><span class="str">    - Avoid additional instructions or information besides the required MongoDB query.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t"><span class="str">10. **Model Usage**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t"><span class="str">    - Refer to the given model schema ('DBSCHEMA') for field names and structure.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t"><span class="str">    - Follow the examples provided for structure and style but do not replicate them.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="str">11. **Date usage**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="str">    - Always use ("START DATE", "CURRENT DATE") (Todays date is 14 august 2024) to use date to generate aggregate queries.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="str">    - However you are generating any query, and in that we have a use of ("START DATE", "CURRENT DATE"), always use this one.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t"><span class="str">    - Do not use dateSubtract and units to calculate the date instead directly put the calculated dates in query using $gt and $lt operators</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t"><span class="str">12. **Chart Type**:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="str">    - Always generate query which result can easily plot the chart mention in input.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t"><span class="str">### Task:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t"><span class="str">Based on the prompt, generate the MongoDB aggregate query according to the guidelines above.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t"><span class="str">Generate the final output strictly following JSON formatting rules and ensuring that the output is in the JSON.stringify format.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t"><span class="str">### Examples</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t"><span class="str">Refer to the given examples solely for context when generating queries,if you find question in these examples use thier output as a query. However, avoid directly replicating these examples to provide the same response:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t"><span class="str">- Example1: Prompt: Which sg are active?, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t"><span class="str">Output: [ { "$match": { "isInactive": { "$ne": True } } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t"><span class="str">- Example2: Prompt: &#8220;Show me the top 5 business processes with most approval counts&#8221;,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$businessProcessDefinition", "approvalCount": { "$sum": { "$size": { "$filter": { "input": "$processSteps", "as": "step", "cond": { "$regexMatch": { "input": "$$step.stepType", "regex": "Approval", "options": "i" } } } } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "approvalCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "approvalCount": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t"><span class="str">- Example3: Prompt: Give me 5 most recently run custom reports, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t"><span class="str">Output: [ { "$sort": { "lastRunDate": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "simplropsId": 1, "customReportName": 1, "lastRunDate": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t"><span class="str">- Example4: Prompt: What are different integration types are there?, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t"><span class="str">Output: [ { "$match": { "technologyType": { "$regex": ".*" } } }, { "$group": { "_id": "$technologyType", "count": { "$sum": 1 } } } , { "$sort": { "count": -1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t"><span class="str">- Example5: Prompt: How many total calc fields are there, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": null, "total": { "$sum": 1 } } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t"><span class="str">- Example6: Prompt: List of all time offs with type Vacation, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t"><span class="str">Output: [ { "$match": { "timeOffType": { "$regex": "Vacation", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "timeOffName": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t"><span class="str">- Example7: Prompt: Give me with top 5 security groups that have highest number of domain policies, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t"><span class="str">Output: [ { "$sort": { "numberOfPolicies": -1 } }, { "$limit": 5 }, { "$project": { "_id": 0, "securityGroupName": 1, "numberOfPolicies": 1, "simplropsId": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t"><span class="str">- Example8: Prompt: List of security groups with report writer access, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t"><span class="str">Output: [ { "securityGroupDomains": { "$elemMatch": { "policyName": "Custom Report Creation", "permissions.canModify": True} } }, { "$project": { "_id": 0, "simplropsId": 1, "securityGroupName": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t"><span class="str">- Example9: Prompt: Give the number of items in the integration where the PII value is 'No', </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t"><span class="str">Output: [ { "$match": { "isPII": False } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t"><span class="str">- Example10: Prompt: What are the source and target names for the integration named AUTO_Change_Jobv35.0?, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t"><span class="str">Output: [ { "$match": { "name": { "$regex": "AUTO_Change_Jobv35.0", "$options": "i" } } }, { "$project": { "_id": 0, "sourceName": "$sourceName", "targetName": "$targetName" } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t"><span class="str">- Example11: Prompt: Give me list of functional areas for the Dependent Event for Global Support - JAPAC Group business process, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t"><span class="str">Output: [ { "$match": { "businessProcessDefinition": {"$regex": "Dependent Event for Global Support - JAPAC Group", "$options": "i"} } }, { "$project": { "_id": 0, "functionalAreas.functionalAreaName": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t"><span class="str">- Example12: Prompt: Please check if there are any security group are duplicated or not, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$securityGroupName", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 1 } } }, { "$project": { "securityGroupName": "$_id", "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t"><span class="str">- Example13: Prompt: &#8220;list of all business processes&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t"><span class="str">Output: [{ "$project": { "_id": 0, "simplropsId": 1, NAMEFIELD: 1 } }].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t"><span class="str">- Example14: Prompt: &#8220;What are the different types of security groups we have&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } }, { "$match": { "count": { "$gt": 0 } } }, { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t"><span class="str">- Example15: Prompt: &#8220;List all the versions available for "Ready for Hire Business Process Ready For Hire (Default Definition)".&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t"><span class="str">Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Accounting Journal Event \\(Default Definition\\)", "$options": "i" } } }, { "$group": { "_id": "$versionCount", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "version": "$_id", "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t"><span class="str">- Example16: Prompt: &#8220;Please list all the permissions for "Hire (Default Definition) Business Process" that an HR administrator has access to.&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="str">Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Hire \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$permissions" }, { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t"><span class="str">- Example17: Prompt: &#8220;List some of the business processes with more than 2 steps&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$simplropsId", "stepCount": { "$sum": { "$size": "$processSteps" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" } } }, { "$match": { "stepCount": { "$gt": 2 } } }, { "$project": { "_id": 0, "simplropsId": "$_id", "businessProcessDefinition": 1, "stepCount": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t"><span class="str">- Example18: Prompt: &#8220;Please list all the Business Process Permissions that an HR administrator has access to.&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t"><span class="str">Output: [ { "$match": { "permissions": { "$regex": "View all", "$options": "i" } } }, { "$project": { "_id": 0, "simplropsId": 1, "businessProcessDefinition": 1, "permissions": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t"><span class="str">- Example19: Prompt: &#8220;Please list all the domain policies that an HR administrator has access to.&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t"><span class="str">Output: [ { "$match": { "securityGroupName": { "$regex": "HR Administrator", "$options": "i" } } }, { "$unwind": "$securityGroupDomains" }, { "$group": { "_id": "$securityGroupDomains.policyName", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "_id": 0, "policyName": "$_id", "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t"><span class="str">- Example20: Prompt: &#8220;How many tenant Health Assessments have been run so far in SimplrOps for this tenant?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t"><span class="str">Output: [ { "$match": { "tenantId": `Current tenant id required` } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t"><span class="str">- Example21: Prompt: &#8220;How many Release management analyses have been run so far?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t"><span class="str">Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46" } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t"><span class="str">- Example22: Prompt: &#8220;How many Release management analyses have been run in the last year?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t"><span class="str">Output: [ { "$match": { "tenantId": "CP105_TENANT_ID46", "startTime": { "$gte": ISODate("2023-07-01T00:00:00.000Z"), "$lt": ISODate("2024-07-01T00:00:00.000Z") } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t"><span class="str">- Example23: Prompt: &#8220;How many organizations do not have a supervisory organization?, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t"><span class="str">Output: [ { "$match": { "isArchive": { "$ne": True } } }, { "$match": { "superiorOrgWid": { "$exists": True } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t"><span class="str">- Example24: Prompt: &#8220;List some of the interfaces that have more than 2 steps&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t"><span class="str">Output: [ { "$match": { "numberOfProcessSteps": { "$gt": 2 } } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t"><span class="str">- Example25: Prompt: &#8220;How many training centers are located in different places?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$locale", "count": { "$sum": 1 } } }, { "$project": { "_id": 0, "locale": "$_id", "count": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t"><span class="str">- Example25: Prompt: &#8220;List of organizations where employee count is more than 2?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t"><span class="str">Output: [ { "employeeCount": { "$gt": 2 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t"><span class="str">- Example27: Prompt: &#8220;What is the latest version of integration in SimplrOps?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t"><span class="str">Output: [ { "$sort": { "api": -1 } }, { "$project": { "apiVersion": 1, "_id": 0 } }, { "$limit": 1 } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t"><span class="str">- Example28: Prompt: &#8220;What reports have PII in them?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t"><span class="str">Output: [ { "$match": { "isPII": True } }, { "$project": { "customReportName": 1, "_id": 0 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t"><span class="str">- Example29: Prompt: &#8220;How many Domain security Policies does HR Administrator security group have?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t"><span class="str">Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$project": { "item": 1, "securityGroupDomainsCount": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t"><span class="str">- Example30: Prompt: &#8220;For HR administrator security group, please list all the Domain Policy accesses it has view and modify access to.?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t"><span class="str">Output: [ { "$match": { "securityGroupName": "HR Administrator" } }, { "$unwind": { "path": "$securityGroupDomains", "preserveNullAndEmptyArrays": True } }, { "$match": { "$and": [ { "securityGroupDomains.permissions.canView": True }, { "securityGroupDomains.permissions.canModify": True } ] } }, { "$project": { "_id": 0, "policyName": "$securityGroupDomains.policyName", "simplropsId": 1 } } ].</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t"><span class="str">- Example31: Prompt: &#8220;What are the different types of security groups we have&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t"><span class="str">Output: [  { "$group": { "_id": "$securityGroupType", "count": { "$sum": 1 } } },  { "$match": { "count": { "$gt": 0 } } },  { "$project": { "securityGroupType": "$_id", "count": 1 } }, { "$sort": { "count": -1 } }]&#8221;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t"><span class="str">- Example32: Prompt: &#8220;Show me the business process with most custom notifications&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$simplropsId", "notificationCount": { "$sum": { "$size": "$notifications" } }, "businessProcessDefinition": { "$first": "$businessProcessDefinition" }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "notificationCount": -1 } }, { "$limit": 1 }, { "$project": { "_id":0, "simplropsId": 1, "businessProcessDefinition": 1, "notificationCount": 1 } } ]&#8221;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t"><span class="str">- Example33: Prompt: &#8220;How many business processes has approval steps&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t"><span class="str">Output: [ { $group: { _id: "$simplropsId", approvalStepCount: { $sum: { $size: { $filter: { input: "$processSteps", as: "step", cond: { $regexMatch: { input: "$$step.stepType", regex: "Approval", options: "i" } } } } } }, businessProcessDefinition: { $first: "$businessProcessDefinition" } } }, { $match: { approvalStepCount: { $gt: 0 } } }, { "$count": "count" } ]&#8221;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t"><span class="str">- Example34: Prompt: &#8220;Show the top 5pp.services.ollie_service.query_se business processes with the highest step counts&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t"><span class="str">Output: [ { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1 } }]&#8221;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t"><span class="str">- Example35: Prompt: &#8220;Show me failed integration events for the month of MAY 2024&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t"><span class="str">Output: [ { "$match": { "integration": { "$exists": True } } }, { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" }, "actualStartDateAndTime": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$project": { "_id": 1, "integrationEventRefId": 1 } } ]&#8221; </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t"><span class="str">- Example36: Prompt: &#8220;How many business processes were used last month?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t"><span class="str">Output: [ { "$match": { "processLastUsed": { "$gte": "2024-05-01T00:00:00.000Z", "$lt": "2024-06-01T00:00:00.000Z" } } }, { "$group": { "_id": null, "count": { "$sum": 1 } } }, { "$project": { "count": 1 } }]&#8221; </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t"><span class="str">- Example19: Prompt: &#8220;list of business processes were used last 7 days&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t"><span class="str">Output: [ { "$match": { "processLastUsed": { "$gte": "2024-06-07T00:00:00.000Z", "$lt": "2024-06-14T00:00:00.000Z" } } }, { "$project": { "simplropsId": 1, "businessProcessDefinition": 1 } }]&#8221; </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t"><span class="str">- Example37: Prompt: &#8220;list of failed integrations&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t"><span class="str">Output: [ { "$match": { "integrationEventStatus": { "$regex": "Failed", "$options": "i" } } }, { "$lookup": { "from": "integrations", "localField": "integration", "foreignField": "_id", "as": "integration" } }, { "$unwind": { "path": "$integration", "preserveNullAndEmptyArrays": False } }, { "$group": { "_id": "$integration._id", "root": { "$first": "$$ROOT" } } }, { "$replaceRoot": { "newRoot": "$root" } }, { "$addFields": { "integrationEventRefId": "$integration.name", "link": "DCI" } }, { "$project": { "link": 1, "simplropsId": 1, "integrationEventRefId": 1 } } ]&#8221; </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t"><span class="str">- Example38: Prompt: &#8220;List different process step types of Add Additional Job (Default Definition) business process?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t"><span class="str">Output: [ { "$match": { "businessProcessDefinition": { "$regex": "Add Additional Job \\(Default Definition\\)", "$options": "i" } } }, { "$unwind": "$processSteps" }, { "$group": { "_id": "$processSteps.stepType", "count": { "$sum": 1 } } }, { "$sort": { "count": -1 } }, { "$project": { "stepType": "$_id", "count": 1 } }]&#8221; </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t"><span class="str">Example 39: Prompt: &#8220;Count of integration not retrieved in digital configuration inventory&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { isFullConfigRetrieve: { $ne: 'Yes' } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t"><span class="str">Example 40: Prompt: &#8220;How many release management analyses have been run so far?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $group: { _id: null, count: { $sum: 1 } } }, { $project: { _id: 0, count: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t"><span class="str">Example 41: Prompt: &#8220;Please list all the business process permissions that HR administrator has access to?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $unwind: "$permissions" }, { $group: { _id: "$permissions", count: { $sum: 1 } } }, { $sort: { count: -1 } }, { $project: { permission: "$_id", count: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t"><span class="str">Example 42: Prompt: &#8220;List of integrations that have expiring schedules in this month?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t"><span class="str">Example 43: Prompt: &#8220;Count of integrations retrieved in DCI&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t"><span class="str">Output: [{ "$match": { "tenantId": "CP105_TENANT_ID46", "isArchive": { "$ne": True } } }, { "$count": "matchingDocuments" } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t"><span class="str">Example 44: Prompt: &#8220;List of integrations that have expiring schedules in July?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate("2024-08-01T00:00:00.000Z"), $lt: ISODate("2024-09-01T00:00:00.000Z") } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t"><span class="str">Example 45: Prompt: &#8220;List of condition rules&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t"><span class="str">Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t"><span class="str">Example 46: Prompt: &#8220;List of condition rules in DCI&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t"><span class="str">Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$project": { "link": 1, "simplropsId": 1, "rule": 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t"><span class="str">Example 47: Prompt: &#8220;What is the permission count for 'implementers' security groups?&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t"><span class="str">Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$match": { "securityGroupName": { "$regex": "implementers", "$options": "i" } } }, { "$group": { "_id": "$securityGroupName", "permissionsCount": { "$sum": { "$cond": { "if": { "$isArray": "$securityGroupDomains" }, "then": { "$size": "$securityGroupDomains" }, "else": 0 } } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$project": { "_id": 0, "simplropsId": 1, "permissionsCount": 1 } }, { "$group": { "_id": null, "totalPermissionsCount": { "$sum": "$permissionsCount" }, "details": { "$push": { "simplropsId": "$simplropsId", "permissionsCount": "$permissionsCount" } } } }, { "$project": { "_id": 0, "totalPermissionsCount": 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t"><span class="str">Example 48: Prompt: &#8220;Show me top 5 business processes with the highest step counts.&#8221;, </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t"><span class="str">Output: [{ "$match": { "isArchive": { "$ne": True } } }, { "$group": { "_id": "$businessProcessDefinition", "stepCount": { "$sum": { "$size": "$processSteps" } }, "simplropsId": { "$first": "$simplropsId" } } }, { "$sort": { "stepCount": -1 } }, { "$limit": 5 }, { "$project": { "simplropsId": 1, "businessProcessDefinition": "$_id", "stepCount": 1, "_id": 0 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t"><span class="str">Example 49: Prompt: "List of scheduled integrations that will run in the next 6 hours."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t"><span class="str">Output: [{ $match: { isArchive: { $ne: True } } }, { $match: { schedule: { $elemMatch: { nextScheduledDateTime: { $gte: ISODate( ""2024-08-06T00:00:00.000Z"" ), $lt: ISODate( ""2024-08-06T06:00:00.000Z"" ) } } } } }, { $project: { _id: 0, simplropsId: 1, name: 1 } }]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t"><span class="str">Example 50: Prompt: &#8220;Which SG has access to ad Hoc approval for the hire (Default Definition) Business process?&#8221;,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t"><span class="str">Output: [ { $match: {isArchive: { $ne: True } } }, { $lookup: { from: "businessGroups", let: { localBusinessProcessType: "$businessProcessTypesAccess.businessProcessType" }, pipeline: [ { $match: { $expr: { $and: [ { $regexMatch: { input: "$businessProcessDefination", regex: "Hire \(Default Definition\)", options: "i" } }, { $eq: [ "$businessProcessType", "$$localBusinessProcessType" ] } ] } } } ], as: "bp" } }, { $match: { "businessProcessTypesAccess.businessProcessTypesGrantedToSecurityGroupApproveAccess": True } }, { $project: { _id: 0, simplropsId: 1, securityGroupName: 1 } } ]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t"><span class="str">Generated Output should include:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t"><span class="str">1. **MongoDB Aggregate Query**: JSON array `[{ }]` containing the required stages (`$group`, `$match`, `$project`).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t"><span class="str">**Note**: </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t"><span class="str">    - Ensure the MongoDB query aligns properly with the identified database collection to achieve expected results without additional text.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t"><span class="str">    - Generate query according to given schema,query_type and chart_type</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t"><span class="str">    - query_type is count then generate query that returns count, if query_type is data then generate query that returns single data, If query_type is list then generate query that returns list of details </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t"><span class="str">    - Provide only those fields that need to generate asked chart</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t"><span class="str">here is,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t"><span class="str">**Input Prompt**: {prompt}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t"><span class="str">**Query Type**: {query_type}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t"><span class="str">**Mongo schema**: {schema}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t"><span class="str">**Chart Type**: {chart_type}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t"><span class="key">def</span> <span class="nam">preprocess_query</span><span class="op">(</span><span class="nam">query</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">    <span class="nam">query</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">        <span class="nam">query</span><span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">"`"</span><span class="op">,</span> <span class="str">""</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">"json"</span><span class="op">,</span> <span class="str">""</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">        <span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">"html"</span><span class="op">,</span> <span class="str">""</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">        <span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>  <span class="com"># Remove leading and trailing whitespace including newlines</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">    <span class="key">return</span> <span class="nam">query</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t"><span class="key">def</span> <span class="nam">get_query</span><span class="op">(</span><span class="nam">instructions</span><span class="op">,</span> <span class="nam">question</span><span class="op">,</span> <span class="nam">query_type</span><span class="op">,</span> <span class="nam">chart_type</span><span class="op">,</span> <span class="nam">schema</span><span class="op">,</span> <span class="nam">history</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">    <span class="nam">messages</span> <span class="op">=</span> <span class="op">[</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"system"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="nam">instructions</span><span class="op">}</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">    <span class="key">for</span> <span class="nam">message</span> <span class="key">in</span> <span class="nam">history</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">        <span class="nam">messages</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"user"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="nam">message</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">        <span class="nam">messages</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"assistant"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="nam">message</span><span class="op">[</span><span class="num">1</span><span class="op">]</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">    <span class="nam">messages</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">        <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">            <span class="str">"role"</span><span class="op">:</span> <span class="str">"user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">            <span class="str">"content"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">                <span class="op">{</span><span class="str">"type"</span><span class="op">:</span> <span class="str">"text"</span><span class="op">,</span> <span class="str">"text"</span><span class="op">:</span> <span class="nam">question</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">                <span class="op">{</span><span class="str">"type"</span><span class="op">:</span> <span class="str">"text"</span><span class="op">,</span> <span class="str">"text"</span><span class="op">:</span> <span class="nam">query_type</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">                <span class="op">{</span><span class="str">"type"</span><span class="op">:</span> <span class="str">"text"</span><span class="op">,</span> <span class="str">"text"</span><span class="op">:</span> <span class="nam">chart_type</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">                <span class="op">{</span><span class="str">"type"</span><span class="op">:</span> <span class="str">"text"</span><span class="op">,</span> <span class="str">"text"</span><span class="op">:</span> <span class="nam">schema</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">    <span class="nam">response</span> <span class="op">=</span> <span class="nam">client</span><span class="op">.</span><span class="nam">chat</span><span class="op">.</span><span class="nam">completions</span><span class="op">.</span><span class="nam">create</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">        <span class="nam">model</span><span class="op">=</span><span class="str">"simplrops-gpt-4o"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">        <span class="nam">messages</span><span class="op">=</span><span class="nam">messages</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">        <span class="nam">temperature</span><span class="op">=</span><span class="num">0.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">        <span class="nam">max_tokens</span><span class="op">=</span><span class="num">256</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">        <span class="nam">top_p</span><span class="op">=</span><span class="num">0.2</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">        <span class="nam">frequency_penalty</span><span class="op">=</span><span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">        <span class="nam">presence_penalty</span><span class="op">=</span><span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">        <span class="nam">response_format</span><span class="op">=</span><span class="op">{</span><span class="str">"type"</span><span class="op">:</span> <span class="str">"text"</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">    <span class="key">return</span> <span class="nam">response</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">message</span><span class="op">.</span><span class="nam">content</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_84b4a12393a39fbf_classifier_service_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_84b4a12393a39fbf_simplrops_context_service_chroma_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.9">coverage.py v7.6.9</a>,
            created at 2025-06-17 10:33 +0530
        </p>
    </div>
</footer>
</body>
</html>
