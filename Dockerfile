FROM python:3.10-alpine

RUN pip install --upgrade pip setuptools

ENV DATABASE_URL=test
ENV DATABASE_NAME=test
ENV JWTSECRET=test
ENV OPENAI_API_KEY=test

WORKDIR /src

# Set the user as root
USER root

# Define the group and user id
ENV GROUP_ID=10000 \
    USER_ID=10000

# Create group and user
RUN addgroup --gid "$GROUP_ID" sops \
    && adduser --disabled-password --uid "$USER_ID" --ingroup sops sops


COPY . .

RUN pip install -r requirements.txt

# Set ownership of the app directory
RUN chown -R sops: /src/

# Set the user as the sops user
USER sops

# Expose the port
EXPOSE 5000

CMD ["uvicorn", "app.server:app", "--host", "0.0.0.0", "--port", "5000"]